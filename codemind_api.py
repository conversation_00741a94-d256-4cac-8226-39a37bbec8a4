"""
CodeMind API - 提供代码库索引和搜索功能的守护进程接口

本模块提供几个主要接口：
1. 触发代码块分割和嵌入生成 (index_codebase)
2. 进行代码库语义搜索 (codebase_search)
3. 进行代码库Grep搜索 (grep_search)
4. 知识库搜索 (knowledge_search)
5. 扩展源搜索 (extension_search)
6. 扩展源索引状态查询 (extension_status)
7. 获取缓存目录路径 (cache_directory)

注意：
- repo_directory 索引时不再使用 LLM 生成摘要
- extension_sources 在后台异步构建索引和向量化（包含 LLM 摘要）
- extension_sources 的索引存储在项目特定的缓存目录/extension_dir 目录
- 使用守护进程方式运行，支持进程复用和优雅关闭
"""

import logging
import os
import sys
import json
import signal
import socket
import hashlib
import threading
import multiprocessing
from typing import Optional, Dict, Any
from dataclasses import dataclass

# 导入API功能
from api import (
    index_codebase,
    codebase_search,
    grep_search,
    knowledge_search,
    extension_search
)

# 导入配置
from config.server import HOME_DIR

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class DaemonConfig:
    """守护进程配置"""
    repo_path: str
    daemon_id: str
    pid_file: str
    socket_file: str
    log_file: str
    cache_dir: str

class CodeMindDaemon:
    """CodeMind 守护进程类"""

    def __init__(self, config: DaemonConfig):
        self.config = config
        self.running = False
        self.server_socket = None
        self.shutdown_event = threading.Event()

        # 设置信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

    def _log_socket_error(self, error: Exception, context: str):
        """记录socket错误的详细信息"""
        error_type = type(error).__name__
        error_msg = str(error)
        logger.warning(f"{context} - Socket错误 [{error_type}]: {error_msg}")

        # 为特定错误类型提供更多信息
        if isinstance(error, BrokenPipeError):
            logger.info("Broken pipe错误通常表示客户端在服务端完成响应之前断开了连接")
        elif isinstance(error, ConnectionResetError):
            logger.info("Connection reset错误表示连接被对端重置")
        elif isinstance(error, ConnectionAbortedError):
            logger.info("Connection aborted错误表示连接被本地软件中止")

    def _signal_handler(self, signum, _):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        self.shutdown()

    @staticmethod
    def get_daemon_config(repo_path: str) -> DaemonConfig:
        """获取守护进程配置"""
        daemon_id = CodeMindDaemon._generate_daemon_id(repo_path)
        daemon_dir = os.path.join(HOME_DIR, ".codemind_caches/daemons", daemon_id)
        os.makedirs(daemon_dir, exist_ok=True)

        return DaemonConfig(
            repo_path=repo_path,
            daemon_id=daemon_id,
            pid_file=os.path.join(daemon_dir, "daemon.pid"),
            socket_file=os.path.join(daemon_dir, "daemon.sock"),
            log_file=os.path.join(daemon_dir, "daemon.log"),
            cache_dir=daemon_dir
        )

    @staticmethod
    def _generate_daemon_id(repo_path: str) -> str:
        """基于仓库路径生成唯一的守护进程标识符"""
        abs_path = os.path.abspath(repo_path)
        return hashlib.md5(abs_path.encode()).hexdigest()[:16]

    @staticmethod
    def _get_project_cache_dir(repo_path: str) -> str:
        """基于仓库路径生成项目特定的缓存目录"""
        repo_path_md5 = hashlib.md5(os.path.abspath(repo_path).encode()).hexdigest()
        return os.path.join(HOME_DIR, f".codemind_caches/{repo_path_md5}")


    def _is_daemon_running(self) -> bool:
        """检查守护进程是否正在运行"""
        # 首先检查PID文件是否存在
        if not os.path.exists(self.config.pid_file):
            # PID文件不存在，清理可能的残留socket文件
            self._cleanup_files()
            return False

        try:
            with open(self.config.pid_file, 'r') as f:
                pid = int(f.read().strip())

            # 检查进程是否存在
            os.kill(pid, 0)

            # 进程存在，但还需要检查socket是否可用
            if not self._test_socket_connection():
                logger.warning(f"守护进程PID {pid} 存在但socket连接失败，清理残留文件")
                self._cleanup_files()
                return False

            return True

        except (OSError, ValueError, ProcessLookupError):
            # PID文件损坏或进程不存在，清理文件
            logger.info("PID文件损坏或进程不存在，清理残留文件")
            self._cleanup_files()
            return False

    def _test_socket_connection(self) -> bool:
        """测试socket连接是否可用"""
        if not os.path.exists(self.config.socket_file):
            return False

        try:
            # 尝试连接socket
            test_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            test_socket.settimeout(2.0)  # 2秒超时
            test_socket.connect(self.config.socket_file)
            test_socket.close()
            return True
        except (socket.error, OSError, ConnectionRefusedError):
            return False

    def _cleanup_files(self):
        """清理守护进程文件"""
        for file_path in [self.config.pid_file, self.config.socket_file]:
            if os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                except OSError:
                    pass

    def _write_pid_file(self):
        """写入PID文件"""
        with open(self.config.pid_file, 'w') as f:
            f.write(str(os.getpid()))

    def _create_socket(self):
        """创建Unix域套接字"""
        if os.path.exists(self.config.socket_file):
            os.unlink(self.config.socket_file)

        self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        self.server_socket.bind(self.config.socket_file)
        self.server_socket.listen(5)
        logger.info(f"守护进程监听套接字: {self.config.socket_file}")

    def _handle_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理API请求"""
        try:
            action = request_data.get('action')
            params = request_data.get('params', {})

            if action == 'index_codebase':
                result = index_codebase(
                    repo_directory=params.get('repo_directory'),
                    force_reindex=params.get('force_reindex', False),
                    extension_sources=params.get('extension_sources'),
                    llm_summary=params.get('llm_summary', False),
                    enable_contextual_retrieval=params.get('contextual_retrieval', False)
                )
                formatted_message = f"索引结果: {result}"
                return {'success': True, 'message': formatted_message}

            elif action == 'codebase_search':
                result = codebase_search(
                    repo_directory=params.get('repo_directory'),
                    query=params.get('query'),
                    limit=params.get('limit', 5),
                    use_rerank=params.get('use_rerank', False),
                    min_score=params.get('min_score', 0.5),
                    complete_imports=params.get('complete_imports', False),
                    call_graph=params.get('call_graph', False),
                    use_llm_filter=params.get('use_llm_filter', True)
                )
                formatted_lines = []
                formatted_lines.append(f"搜索结果状态: {result['code']} - {result['msg']}")
                formatted_lines.append(f"搜索耗时: {result['cost_time']:.2f} 秒")
                formatted_lines.append(f"搜索结果数量: {len(result['data'])}")

                for i, result_item in enumerate(result['data']):
                    formatted_lines.append(f"结果 {i+1}:")
                    formatted_lines.append(f"  查询: {result_item['query']}")
                    formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")
                    formatted_lines.append(f"  内容片段: {result_item['passage']}")

                    if 'call_chains' in result_item and result_item['call_chains']:
                        formatted_lines.append(f"  函数调用链 ({len(result_item['call_chains'])} 条):")
                        for j, chain in enumerate(result_item['call_chains']):
                            formatted_lines.append(f"    调用链 {j+1}: {chain}")
                        formatted_lines.append("")

                formatted_message = "\n".join(formatted_lines)
                return {'success': True, 'message': formatted_message}
            
            elif action == 'grep_search':
                limit = params.get('limit', 5)
                result = grep_search(
                    repo_directory=params.get('repo_directory'),
                    query=params.get('query'),
                    context_lines=params.get('context_lines', 5),
                    limit=limit
                )
                formatted_lines = []
                formatted_lines.append(f"搜索结果状态: {result['code']} - {result['msg']}")
                formatted_lines.append(f"搜索耗时: {result['cost_time']:.2f} 秒")
                if result['code'] == 0:
                    formatted_lines.append(f"搜索结果数量: {len(result['data'])}")
                    for i, result_item in enumerate(result['data'][:limit]):
                        formatted_lines.append(f"结果 {i+1}:")
                        formatted_lines.append(f"  查询: {result_item['query']}")
                        formatted_lines.append(f"  匹配次数: {result_item['score']}")
                        formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")

                formatted_message = "\n".join(formatted_lines)
                return {'success': True, 'message': formatted_message}

            elif action == 'knowledge_search':
                result = knowledge_search(
                    query=params.get('query'),
                    limit=params.get('limit', 3),
                    timeout=params.get('timeout', 30)
                )
                formatted_lines = []
                formatted_lines.append(f"搜索结果状态: {result['code']} - {result['msg']}")
                formatted_lines.append(f"搜索耗时: {result['cost_time']:.2f} 秒")
                if result['code'] == 0 and 'replies' in result['data']:
                    formatted_lines.append(f"搜索结果数量: {len(result['data']['replies'])}")
                    formatted_lines.append(f"搜索结果: {result['data']['replies']}")

                formatted_message = "\n".join(formatted_lines)
                return {'success': True, 'message': formatted_message}

            elif action == 'extension_search':
                limit = params.get('limit', 5)
                result = extension_search(
                    repo_directory=params.get('repo_directory'),
                    query=params.get('query'),
                    limit=limit,
                    use_rerank=params.get('use_rerank', False),
                    min_score=params.get('min_score', 0.5)
                )
                formatted_lines = []
                formatted_lines.append(f"搜索结果状态: {result['code']} - {result['msg']}")
                formatted_lines.append(f"搜索耗时: {result['cost_time']:.2f} 秒")
                if result['code'] == 0:
                    formatted_lines.append(f"搜索结果数量: {len(result['data'])}")
                    for i, result_item in enumerate(result['data'][:limit]):
                        formatted_lines.append(f"结果 {i+1}:")
                        formatted_lines.append(f"  查询: {result_item['query']}")
                        formatted_lines.append(f"  相关度: {result_item['score']}")
                        formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")
                        formatted_lines.append(f"  来源类型: {result_item.get('source_type', 'unknown')}")
                        formatted_lines.append(f"  内容片段: {result_item['passage']}")

                formatted_message = "\n".join(formatted_lines)
                return {'success': True, 'message': formatted_message}

            elif action == 'cache_directory':
                # 返回项目特定的缓存目录（基于仓库路径MD5）
                repo_path_md5 = hashlib.md5(os.path.abspath(self.config.repo_path).encode()).hexdigest()
                project_cache_dir = os.path.join(HOME_DIR, f".codemind_caches/{repo_path_md5}")
                return {'success': True, 'message': project_cache_dir}

            elif action == 'shutdown':
                self.shutdown()
                return {'success': True, 'message': '守护进程正在关闭'}

            else:
                return {'success': False, 'message': f'未知操作: {action}'}

        except Exception as e:
            logger.error(f"处理请求失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    def _handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            # 设置socket超时 - 增加到5分钟以支持长时间的索引操作
            client_socket.settimeout(300)  # 300秒超时

            # 接收请求数据
            data = b''
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
                if b'\n\n' in data:  # 简单的消息结束标记
                    break

            if not data:
                return

            # 解析请求
            request_str = data.decode('utf-8').strip()
            request_data = json.loads(request_str)

            # 处理请求
            response = self._handle_request(request_data)

            # 发送响应 - 使用sendall确保完整发送
            response_str = json.dumps(response, ensure_ascii=False)
            response_bytes = response_str.encode('utf-8')
            client_socket.sendall(response_bytes)

        except socket.timeout:
            logger.error("客户端请求超时")
            error_response = {'success': False, 'error': '请求超时'}
            try:
                client_socket.sendall(json.dumps(error_response).encode('utf-8'))
            except:
                pass
        except (BrokenPipeError, ConnectionResetError, ConnectionAbortedError) as e:
            # 客户端连接中断，这是正常情况，不需要发送错误响应
            self._log_socket_error(e, "处理客户端连接")
        except json.JSONDecodeError as e:
            logger.error(f"请求格式错误: {str(e)}")
            error_response = {'success': False, 'error': f'请求格式错误: {str(e)}'}
            try:
                client_socket.sendall(json.dumps(error_response).encode('utf-8'))
            except (BrokenPipeError, ConnectionResetError, ConnectionAbortedError):
                # 客户端已断开，无法发送错误响应
                pass
            except:
                pass
        except Exception as e:
            logger.error(f"处理客户端连接失败: {str(e)}")
            error_response = {'success': False, 'error': str(e)}
            try:
                client_socket.sendall(json.dumps(error_response).encode('utf-8'))
            except (BrokenPipeError, ConnectionResetError, ConnectionAbortedError):
                # 客户端已断开，无法发送错误响应
                pass
            except:
                pass
        finally:
            try:
                client_socket.close()
            except:
                pass

    def start(self):
        """启动守护进程"""
        if self._is_daemon_running():
            logger.info(f"守护进程已在运行 (PID文件: {self.config.pid_file})")
            return False

        # 启动前强制清理可能的残留文件
        logger.info("启动前清理可能的残留文件...")
        self._cleanup_files()

        # 设置缓存目录环境变量 - 基于仓库路径MD5区分不同项目
        project_cache_dir = self._get_project_cache_dir(self.config.repo_path)
        os.environ['CACHE_DIRECTORY'] = project_cache_dir
        os.makedirs(project_cache_dir, exist_ok=True)

        logger.info(f"设置项目缓存目录: {project_cache_dir} (仓库: {self.config.repo_path})")

        # 创建守护进程特定的目录
        os.makedirs(self.config.cache_dir, exist_ok=True)

        # 写入PID文件
        self._write_pid_file()

        # 创建套接字
        self._create_socket()

        self.running = True
        logger.info(f"守护进程启动成功 (PID: {os.getpid()}, 仓库: {self.config.repo_path})")

        # 主循环
        while self.running and not self.shutdown_event.is_set():
            try:
                self.server_socket.settimeout(1.0)  # 设置超时以便检查shutdown_event
                client_socket, _ = self.server_socket.accept()

                # 在新线程中处理客户端请求
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket,),
                    daemon=True
                )
                client_thread.start()

            except socket.timeout:
                continue
            except Exception as e:
                if self.running:
                    logger.error(f"接受连接失败: {str(e)}")
                break

        self._cleanup()
        return True

    def start_background(self):
        """在后台启动守护进程"""
        if self._is_daemon_running():
            logger.info(f"守护进程已在运行 (PID文件: {self.config.pid_file})")
            return True

        # 启动前强制清理可能的残留文件
        logger.info("后台启动前清理可能的残留文件...")
        self._cleanup_files()

        # 使用subprocess在后台启动守护进程
        import subprocess
        import sys

        try:
            # 设置缓存目录环境变量
            project_cache_dir = self._get_project_cache_dir(self.config.repo_path)
            env = os.environ.copy()
            env['CACHE_DIRECTORY'] = project_cache_dir

            logger.info(f"设置项目缓存目录: {project_cache_dir} (仓库: {self.config.repo_path})")

            # 启动后台守护进程
            cmd = [sys.executable, __file__, '--repo-path', self.config.repo_path, 'start']

            # 在后台启动进程
            subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.DEVNULL,  # 忽略输出
                stderr=subprocess.DEVNULL,  # 忽略错误输出
                start_new_session=True  # 创建新的进程组，避免与父进程关联
            )

            # 等待一段时间让守护进程启动
            import time
            max_retries = 10
            for _ in range(max_retries):
                time.sleep(0.5)
                if self._is_daemon_running():
                    logger.info(f"守护进程启动成功 (仓库: {self.config.repo_path})")
                    return True

            logger.error("守护进程启动超时")
            return False

        except Exception as e:
            logger.error(f"启动后台守护进程失败: {str(e)}")
            return False

    def shutdown(self):
        """关闭守护进程"""
        logger.info("开始关闭守护进程...")
        self.running = False
        self.shutdown_event.set()

    def _cleanup(self):
        """清理资源"""
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        self._cleanup_files()
        logger.info("守护进程已关闭")


class CodeMindClient:
    """CodeMind 客户端类"""

    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        config = CodeMindDaemon.get_daemon_config(repo_path)
        self.daemon = CodeMindDaemon(config)

    def _send_request(self, action: str, params: Dict[str, Any] = None, retry_count: int = 2) -> Dict[str, Any]:
        """向守护进程发送请求，支持自动重试和恢复"""
        if params is None:
            params = {}

        request_data = {
            'action': action,
            'params': params
        }

        for attempt in range(retry_count + 1):
            try:
                # 连接到守护进程
                client_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
                client_socket.settimeout(300)  # 根据操作类型设置超时
                client_socket.connect(self.daemon.config.socket_file)

                # 发送请求
                request_str = json.dumps(request_data, ensure_ascii=False) + '\n\n'
                client_socket.send(request_str.encode('utf-8'))

                # 接收响应 - 改进的接收逻辑
                response_data = b''
                while True:
                    try:
                        chunk = client_socket.recv(4096)
                        if not chunk:
                            break
                        response_data += chunk

                        # 检查是否接收完整（避免UTF-8解码错误）
                        try:
                            # 先尝试解码，如果失败说明UTF-8字符被截断
                            response_str = response_data.decode('utf-8')
                            # 再尝试解析JSON，如果成功说明数据完整
                            json.loads(response_str)
                            break  # 解析成功，数据完整
                        except UnicodeDecodeError:
                            # UTF-8字符被截断，继续接收
                            continue
                        except json.JSONDecodeError:
                            # JSON不完整，继续接收
                            continue
                    except socket.timeout:
                        break

                # 最终解码和解析
                try:
                    response_str = response_data.decode('utf-8')
                    response = json.loads(response_str)
                except UnicodeDecodeError as e:
                    logger.error(f"UTF-8解码失败: {str(e)}")
                    return {'success': False, 'error': f'UTF-8解码失败: {str(e)}'}
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    return {'success': False, 'error': f'JSON解析失败: {str(e)}'}

                client_socket.close()
                return response

            except (ConnectionRefusedError, FileNotFoundError, socket.timeout) as e:
                logger.warning(f"守护进程连接失败 (尝试 {attempt + 1}/{retry_count + 1}): {str(e)}")

                if attempt < retry_count:
                    # 尝试重启守护进程
                    logger.info("尝试重启守护进程...")
                    if self._restart_daemon():
                        logger.info("守护进程重启成功，重试请求...")
                        continue
                    else:
                        logger.error("守护进程重启失败")

                return {'success': False, 'error': f'守护进程不可用: {str(e)}'}

            except json.JSONDecodeError as e:
                logger.error(f"响应解析失败: {str(e)}")
                return {'success': False, 'error': f'响应格式错误: {str(e)}'}

            except Exception as e:
                logger.error(f"发送请求失败 (尝试 {attempt + 1}/{retry_count + 1}): {str(e)}")

                if attempt < retry_count:
                    import time
                    time.sleep(1)  # 等待1秒后重试
                    continue

                return {'success': False, 'error': str(e)}

    def ensure_daemon_running(self) -> bool:
        """确保守护进程正在运行"""
        if self.daemon._is_daemon_running():
            return True

        # 启动守护进程
        logger.info(f"启动守护进程 (仓库: {self.repo_path})")
        return self.daemon.start_background()

    def _restart_daemon(self) -> bool:
        """重启守护进程"""
        try:
            logger.info("开始重启守护进程...")

            # 先尝试优雅停止现有守护进程
            if self.daemon._is_daemon_running():
                logger.info("尝试优雅停止现有守护进程...")
                try:
                    self._send_request('shutdown', retry_count=0)
                except:
                    logger.warning("优雅停止失败，将强制清理")

                # 等待进程完全停止
                import time
                for _ in range(5):
                    if not self.daemon._is_daemon_running():
                        logger.info("守护进程已停止")
                        break
                    time.sleep(1)
                else:
                    logger.warning("守护进程停止超时，将强制清理")

            # 强制清理残留文件（无论进程是否存在）
            logger.info("清理守护进程残留文件...")
            self.daemon._cleanup_files()

            # 启动新的守护进程
            logger.info("启动新的守护进程...")
            return self.daemon.start_background()

        except Exception as e:
            logger.error(f"重启守护进程失败: {str(e)}")
            return False

    # API 方法
    def index_codebase(self, force_reindex: bool = False, extension_sources: Optional[str] = None,
                      llm_summary: bool = False, contextual_retrieval: bool = False) -> Dict[str, Any]:
        """索引代码库"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('index_codebase', {
            'repo_directory': self.repo_path,
            'force_reindex': force_reindex,
            'extension_sources': extension_sources,
            'llm_summary': llm_summary,
            'contextual_retrieval': contextual_retrieval
        })

    def codebase_search(self, query: str, limit: int = 20, use_rerank: bool = False,
                       min_score: float = 0, complete_imports: bool = False,
                       call_graph: bool = False, use_llm_filter: bool = True) -> Dict[str, Any]:
        """搜索代码库"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('codebase_search', {
            'repo_directory': self.repo_path,
            'query': query,
            'limit': limit,
            'use_rerank': use_rerank,
            'min_score': min_score,
            'complete_imports': complete_imports,
            'call_graph': call_graph,
            'use_llm_filter': use_llm_filter
        })

    def grep_search(self, query: str, context_lines: int = 5, limit: int = 5) -> Dict[str, Any]:
        """使用grep搜索"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('grep_search', {
            'repo_directory': self.repo_path,
            'query': query,
            'context_lines': context_lines,
            'limit': limit
        })

    def knowledge_search(self, query: str, limit: int = 3, timeout: int = 30) -> Dict[str, Any]:
        """知识库搜索"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('knowledge_search', {
            'query': query,
            'limit': limit,
            'timeout': timeout
        })

    def extension_search(self, query: str, limit: int = 5, use_rerank: bool = False,
                        min_score: float = 0.5) -> Dict[str, Any]:
        """扩展源搜索"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('extension_search', {
            'repo_directory': self.repo_path,
            'query': query,
            'limit': limit,
            'use_rerank': use_rerank,
            'min_score': min_score
        })

    def get_cache_directory(self) -> Dict[str, Any]:
        """获取缓存目录路径"""
        if not self.ensure_daemon_running():
            return {'success': False, 'error': '无法启动守护进程'}

        return self._send_request('cache_directory')

    def shutdown_daemon(self) -> Dict[str, Any]:
        """关闭守护进程"""
        if not self.daemon._is_daemon_running():
            return {'success': True, 'data': {'message': '守护进程未运行'}}

        result = self._send_request('shutdown')

        # 等待守护进程完全停止
        if result.get('success', False):
            import time
            for _ in range(20):  # 最多等待2秒
                if not self.daemon._is_daemon_running():
                    break
                time.sleep(0.1)

        return result

def main():
    """
    主程序入口函数，用于启动守护进程或客户端操作
    """
    import argparse

    multiprocessing.freeze_support()
    multiprocessing_args = [
        '--multiprocessing-fork',
        'tracker_fd=',
        'pipe_handle=',
        '-c',
        'from multiprocessing.resource_tracker import main;main(',
        '-B', '-S', '-I'
    ]

    for arg in sys.argv[1:]:
        if any(mp_arg in str(arg) for mp_arg in multiprocessing_args):
            return

    parser = argparse.ArgumentParser(description="CodeMind 守护进程管理器")
    parser.add_argument("--repo-path", type=str, required=True, help="代码库路径")

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 启动守护进程
    subparsers.add_parser('start', help='启动守护进程')

    # 停止守护进程
    subparsers.add_parser('stop', help='停止守护进程')

    # 状态查询
    subparsers.add_parser('status', help='查询守护进程状态')

    # 测试API
    test_parser = subparsers.add_parser('test', help='测试API功能')
    test_parser.add_argument('--action', type=str, required=True,
                           choices=['index', 'search', 'grep', 'knowledge', 'extension', 'cache'],
                           help='测试的API操作')
    test_parser.add_argument('--query', type=str, help='搜索查询（用于搜索操作）')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    client = CodeMindClient(args.repo_path)

    if args.command == 'start':
        if client.daemon._is_daemon_running():
            print(f"守护进程已在运行 (仓库: {args.repo_path})")
        else:
            print(f"启动守护进程 (仓库: {args.repo_path})")
            # 直接启动守护进程（阻塞模式，用于命令行启动）
            if client.daemon.start():
                print("守护进程启动成功")
            else:
                print("守护进程启动失败")

    elif args.command == 'stop':
        result = client.shutdown_daemon()
        if result['success']:
            print("守护进程已停止")
        else:
            print(f"停止守护进程失败: {result.get('error', '未知错误')}")

    elif args.command == 'status':
        if client.daemon._is_daemon_running():
            print(f"守护进程正在运行")
            print(f"PID文件: {client.daemon.config.pid_file}")
            print(f"套接字文件: {client.daemon.config.socket_file}")
            print(f"缓存目录: {client.daemon.config.cache_dir}")
        else:
            print("守护进程未运行")

    elif args.command == 'test':
        if args.action == 'index':
            result = client.index_codebase()
            print(f"索引结果: {result}")
        elif args.action == 'search' and args.query:
            result = client.codebase_search(args.query)
            print(f"搜索结果: {result}")
        elif args.action == 'grep' and args.query:
            result = client.grep_search(args.query)
            print(f"Grep结果: {result}")
        elif args.action == 'knowledge' and args.query:
            result = client.knowledge_search(args.query)
            print(f"知识库结果: {result}")
        elif args.action == 'extension' and args.query:
            result = client.extension_search(args.query)
            print(f"扩展源结果: {result}")
        elif args.action == 'cache':
            result = client.get_cache_directory()
            print(f"缓存目录: {result}")
        else:
            print("测试操作需要提供 --query 参数（除了 cache 操作）")

if __name__ == "__main__":
    main()