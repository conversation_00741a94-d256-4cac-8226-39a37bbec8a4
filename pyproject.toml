[tool.poetry]
name = "codemind"
version = "0.1.0"
description = ""
authors = ["qintao <<EMAIL>>"]
readme = "README.md"
packages = [
    {include = "*.py"},
    {include = "api"},
    {include = "core"},
    {include = "config"},
    {include = "utils"},
    {include = "llm"},
    {include = "agents"},
    {include = "dataclass"}
]
package-mode = true

[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/" 

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
numpy = "1.26.4"
tqdm = "4.66.3"
loguru = "0.7.2"
pyyaml = "6.0.1"
pydantic = ">=2.0.0"
rapidfuzz = "3.6.2"
redis = "5.0.3"
python-dotenv = "1.0.1"
chardet = "5.2.0"
tantivy = "0.22.0"
diskcache = "5.6.3"
tiktoken = "0.7.0"
pylint = "3.1.0"
tree-sitter = "0.24.0"
tree-sitter-javascript = "0.23.1"
tree-sitter-languages = "1.10.2"
tree-sitter-python = "0.23.6"
tree-sitter-typescript = "0.23.2"
tree-sitter-html = "0.23.1"
tree-sitter-css = "0.23.1"
tree-sitter-json = "0.23.0"

backoff = { version = "2.2.1", python = ">=3.7,<4.0" }
openai = "1.55.3"
scipy = "1.12.0"
voyageai = { version = "0.2.1", python = ">=3.7.1,<4.0.0" }
anthropic = "0.21.3"
networkx = "3.2.1"
pymongo = "4.6.3"
pytz = "2024.1"
parea-ai = { version = "0.2.114", python = ">=3.8.1,<4.0.0" }
pygithub = "2.2.0"
gitpython = "3.1.42"
stringzilla = "3.8.4"
cohere = { version = "5.2.5", python = ">=3.8,<4.0" }
fastapi = ">=0.104.0"
uvicorn = ">=0.24.0"
python-multipart = ">=0.0.6"

[tool.poetry.scripts]
codemind = "codemind_api:main"

[tool.poetry.group.dev.dependencies]
pyinstaller = {version = ">=6.14.1,<7.0.0", python = ">=3.10,<3.14"}

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
