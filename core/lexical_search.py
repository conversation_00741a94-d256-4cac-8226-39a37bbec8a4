from collections.abc import Iterable
import multiprocessing
import os
import re
import subprocess
import time

import tantivy
from diskcache import Cache
from loguru import logger
from redis import Redis
from tqdm import tqdm
from utils.streamable_functions import streamable
from rapidfuzz import fuzz

from utils.timer import Timer
from config.server import get_cache_directory, FILE_CACHE_DISABLED, REDIS_URL
from core.entities import Snippet
from core.repo_parsing_utils import directory_to_chunks
from core.vector_db import multi_get_query_texts_similarity
from dataclass.files import Document
from config.client import SweepConfig

# 使用统一缓存管理器
from utils.cache_manager import get_token_cache, get_snippets_cache
from config.cache_constants import CacheType, EXTENSION_SEED, UNIFIED_CACHE_VERSION
from utils.cache_path_utils import get_lexical_index_cache_path

CACHE_VERSION = UNIFIED_CACHE_VERSION

if FILE_CACHE_DISABLED:
    redis_client = None

# pylint: disable=no-member
schema_builder = tantivy.SchemaBuilder()

# 添加文本字段，设置为存储和索引
schema_builder.add_text_field("title", stored=True)
schema_builder.add_text_field("body", stored=True)
schema_builder.add_integer_field("doc_id", stored=True, indexed=True)
schema = schema_builder.build()
# pylint: enable=no-member

redis_client = Redis.from_url(REDIS_URL) if REDIS_URL else None


class CustomIndex:
    def __init__(self, cache_path: str = None, load_existing: bool = False):
        if cache_path:
            os.makedirs(cache_path, exist_ok=True)

        if load_existing and cache_path and os.path.exists(cache_path):
            # 尝试加载已存在的索引
            try:
                self.index = tantivy.Index.open(cache_path) # pylint: disable=no-member
                logger.info(f"成功加载已存在的索引: {cache_path}")
            except Exception as e:
                logger.warning(f"加载已存在索引失败: {e}，创建新索引")
                self.index = tantivy.Index(schema, path=cache_path) # pylint: disable=no-member
        else:
            # 创建新索引
            if cache_path:
                self.index = tantivy.Index(schema, path=cache_path) # pylint: disable=no-member
            else:
                self.index = tantivy.Index(schema) # pylint: disable=no-member

    def add_documents(self, documents: Iterable):
        writer = self.index.writer()
        for doc_id, (title, text) in enumerate(documents):
            writer.add_document(
                tantivy.Document( # pylint: disable=no-member
                    title=title,
                    body=text,
                    doc_id=doc_id
                )
            )
        writer.commit()
        # 确保索引被刷新到磁盘
        self.index.reload()

    def search_index(self, query: str) -> list[tuple[str, float, dict]]:
        # 对查询进行分词处理，同时处理英文和中文
        tokenized_query = tokenize_code(query)

        # 获取搜索器
        searcher = self.index.searcher() # for some reason, the first searcher is empty
        # 尝试多次获取搜索器，直到找到非空的搜索器
        for _ in range(100):  # 使用下划线表示未使用的变量
            searcher = self.index.searcher()
            if searcher.num_docs > 0:
                break
            time.sleep(0.01)
        else:
            raise Exception("Index is empty")

        # 检查查询是否包含中文字符
        has_chinese = bool(chinese_pattern.search(query))

        if has_chinese:
            # 对于中文查询，使用文档内容直接匹配的方式
            chinese_chars = list(chinese_pattern.findall(query))
            all_results = []

            # 使用搜索器遍历所有文档，检查内容是否包含中文查询
            # 先执行一个通用查询来获取所有文档
            try:
                # 使用一个通用查询来获取所有文档
                universal_query = self.index.parse_query("*", ["title", "body"])
                all_docs = searcher.search(universal_query, searcher.num_docs).hits

                for score, doc_address in all_docs:
                    try:
                        doc = searcher.doc(doc_address)
                        # 使用正确的Tantivy Document API
                        body_list = doc["body"]
                        title_list = doc["title"]

                        body = body_list[0] if body_list else ""
                        title = title_list[0] if title_list else ""

                        # 检查文档内容是否包含查询的中文字符
                        match_score = 0.0
                        for chinese_text in chinese_chars:
                            # 只检查完整词组匹配，不再进行字符级匹配
                            if chinese_text in body or chinese_text in title:
                                match_score += 10.0  # 完整匹配给高分

                        if match_score > 0:
                            all_results.append((match_score, doc_address))

                    except Exception as e:
                        continue

            except Exception as e:
                # 如果通用查询失败，回退到简单的英文查询来获取文档
                try:
                    fallback_query = self.index.parse_query("export", ["title", "body"])
                    fallback_docs = searcher.search(fallback_query, searcher.num_docs).hits

                    for score, doc_address in fallback_docs:
                        try:
                            doc = searcher.doc(doc_address)
                            # 使用正确的Tantivy Document API
                            body_list = doc["body"]
                            title_list = doc["title"]

                            body = body_list[0] if body_list else ""
                            title = title_list[0] if title_list else ""

                            # 检查文档内容是否包含查询的中文字符
                            match_score = 0.0
                            for chinese_text in chinese_chars:
                                # 只检查完整词组匹配，不再进行字符级匹配
                                if chinese_text in body or chinese_text in title:
                                    match_score += 10.0

                            if match_score > 0:
                                all_results.append((match_score, doc_address))

                        except Exception as e:
                            continue
                except:
                    pass

            # 按分数排序并返回结果
            all_results.sort(key=lambda x: x[0], reverse=True)
            return [(searcher.doc(doc_address)["title"][0], score, searcher.doc(doc_address)) for score, doc_address in all_results[:200]]
        else:
            # 对于英文查询，使用原来的方法
            try:
                parsed_query = self.index.parse_query(tokenized_query, ["title", "body"])
                results = searcher.search(parsed_query, 200).hits
                return [(searcher.doc(doc_address)["title"][0], score, searcher.doc(doc_address)) for score, doc_address in results]
            except Exception as e:
                # 如果解析失败，尝试简单的词项查询
                try:
                    # 将查询分解为单个词项
                    terms = tokenized_query.split()
                    if terms:
                        # 使用第一个词项进行搜索
                        parsed_query = self.index.parse_query(f'"{terms[0]}"', ["title", "body"])
                        results = searcher.search(parsed_query, 200).hits
                        return [(searcher.doc(doc_address)["title"][0], score, searcher.doc(doc_address)) for score, doc_address in results]
                except:
                    pass
                return []


variable_pattern = re.compile(r"([A-Z][a-z]+|[a-z]+|[A-Z]+(?=[A-Z]|$))")
# 扩展中文字符范围，包含更多中文相关字符
# \u4e00-\u9fff: CJK统一汉字
# \u3400-\u4dbf: CJK扩展A
# \uf900-\ufaff: CJK兼容汉字
# \u3000-\u303f: CJK符号和标点
chinese_pattern = re.compile(r'[\u3000-\u303f\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff]+')


def tokenize_code(code: str) -> str:
    # 先提取所有中文文本，避免被英文分词干扰
    chinese_texts = []
    chinese_matches = list(chinese_pattern.finditer(code))
    for m in chinese_matches:
        chinese_texts.append((m.start(), m.end(), m.group()))

    # 创建一个临时字符串，将中文部分替换为占位符，避免英文分词时的干扰
    temp_code = code
    for start, end, chinese_text in reversed(chinese_texts):  # 从后往前替换，避免位置偏移
        temp_code = temp_code[:start] + " " * (end - start) + temp_code[end:]

    # 处理英文和数字标识符（在没有中文干扰的临时字符串上）
    matches = re.finditer(r"\b[a-zA-Z0-9_]{2,}\b", temp_code)
    tokens = []
    for m in matches:
        text = m.group()

        # 没有空格的单词都不拆分，保持完整性
        if len(text) > 2:
            # 对于所有连续的单词（包括驼峰、下划线等），只保留完整词，不拆分
            if sum(1 for c in text if 'a' <= c <= 'z' or 'A' <= c <= 'Z' or '0' <= c <= '9') > len(text) // 2 \
                and len(text) / len(set(text)) < 4:
                tokens.append(text.lower())  # 只添加完整词

    # 处理中文字符 - 将中文短语作为一个完整的token，不再按字符分割
    for _, _, chinese_text in chinese_texts:
        # 只添加完整的中文短语作为一个token
        tokens.append(chinese_text)

    return " ".join(tokens)

def snippets_to_docs(snippets: list[Snippet], len_repo_cache_dir):
    docs = []
    for snippet in snippets:
        # 获取代码内容
        code_content = snippet.get_snippet(add_ellipsis=False, add_lines=False)

        # 如果有中文或英文摘要，将其添加到内容中一起索引，但不添加干扰前缀
        content_with_summary = code_content
        if hasattr(snippet, 'summary_zh') and snippet.summary_zh:
            content_with_summary += f"\n{snippet.summary_zh}"
        if hasattr(snippet, 'summary_en') and snippet.summary_en:
            content_with_summary += f"\n{snippet.summary_en}"

        docs.append(
            Document(
                title=f"{snippet.file_path[len_repo_cache_dir:]}:{snippet.start}-{snippet.end}",
                content=content_with_summary,
            )
        )
    return docs


@streamable
def prepare_index_from_snippets(
    snippets: list[Snippet],
    len_repo_cache_dir: int = 0,
    do_not_use_file_cache: bool = False,  # 保留参数以保持API兼容性，但不使用
    cache_path: str = None,
    cache_type: str = "main",  # 新增缓存类型参数
):
    all_docs: list[Document] = snippets_to_docs(snippets, len_repo_cache_dir)
    if len(all_docs) == 0:
        return None
    index = CustomIndex(
        cache_path=cache_path
    )
    yield "Tokenizing documents...", index
    all_tokens = []

    # 根据缓存类型选择对应的token缓存
    current_token_cache = get_token_cache(cache_type)

    try:
        with Timer() as timer:
            for doc in all_docs:
                all_tokens.append(current_token_cache.get(doc.content + CACHE_VERSION))
            misses = [i for i, token in enumerate(all_tokens) if token is None]
            workers = multiprocessing.cpu_count() // 2
            if workers > 1:
                with multiprocessing.Pool(processes=multiprocessing.cpu_count() // 2) as p:
                    missed_tokens = p.map(
                        tokenize_code,
                        tqdm(
                            [all_docs[i].content for i in misses],
                            total=len(misses),
                            desc="Tokenizing documents"
                        )
                    )
            else:
                missed_tokens = [
                    tokenize_code(all_docs[i].content) for i in misses
                ]
            for i, token in enumerate(missed_tokens):
                all_tokens[misses[i]] = token
                current_token_cache[all_docs[misses[i]].content + CACHE_VERSION] = token
        logger.debug(f"Tokenizing documents took {timer.time_elapsed} seconds")
        yield "Building lexical index...", index
        all_titles = [doc.title for doc in all_docs]
        with Timer() as timer:
            index.add_documents(
                tqdm(zip(all_titles, all_tokens), total=len(all_docs), desc="Indexing")
            )
        logger.debug(f"Indexing took {timer.time_elapsed} seconds")
    except FileNotFoundError as e:
        logger.exception(e)

    yield "Index built", index
    return index


def search_index(query: str, index: CustomIndex):
    """Search the index based on a query.

    This function takes a query and an index as input and returns a dictionary of document IDs
    and their corresponding scores.

    支持中文查询：函数会处理中文查询，并返回相关的代码片段。
    """
    # 检查查询是否包含中文字符（使用扩展的中文字符范围）
    has_chinese = bool(chinese_pattern.search(query))

    # 如果查询包含中文，尝试从代码摘要中查找相关内容
    if has_chinese:
        logger.info(f"检测到中文查询: {query}")

    # 使用索引搜索
    results_with_metadata = index.search_index(query)

    # 处理搜索结果
    res = {}
    for doc_id, score, _ in results_with_metadata:
        if doc_id not in res:
            res[doc_id] = score

    # 如果没有找到结果，记录日志
    if len(res) == 0:
        logger.warning(f"查询 '{query}' 没有找到匹配结果")
        max_score = 1
        min_score = 0
    else:
        logger.info(f"查询 '{query}' 找到 {len(res)} 个匹配结果")
        max_score = max(res.values())
        min_score = min(res.values()) if min(res.values()) < max_score else 0

    # 归一化分数
    res = {k: (v - min_score) / (max_score - min_score) for k, v in res.items()}
    return res

SNIPPET_FORMAT = """File path: {file_path}

{contents}"""

def compute_filename_similarity_scores(queries: list[str], snippets: list[Snippet]) -> list[dict]:
    """
    计算查询与文件名的相似度分数

    参数:
        queries: 查询列表
        snippets: 代码片段列表

    返回:
        每个查询对应的文件名相似度分数字典列表
    """
    filename_scores_list = []

    for query in queries:
        filename_scores = {}

        query_terms = query.lower().split()

        for snippet in snippets:
            file_path = snippet.file_path
            filename = os.path.basename(file_path).lower()
            filepath_parts = file_path.lower().split('/')

            max_score = 0.0

            for term in query_terms:
                filename_without_ext = os.path.splitext(filename)[0]
                if term == filename_without_ext:
                    max_score = max(max_score, 1.0)
                    continue

                if term in filename:
                    max_score = max(max_score, 0.8)
                    continue

                for part in filepath_parts:
                    if term in part:
                        max_score = max(max_score, 0.6)
                        break

                fuzzy_score = fuzz.ratio(term, filename_without_ext) / 100.0
                if fuzzy_score > 0.7:
                    max_score = max(max_score, fuzzy_score * 0.5)

                for part in filepath_parts:
                    part_fuzzy_score = fuzz.ratio(term, part) / 100.0
                    if part_fuzzy_score > 0.7:
                        max_score = max(max_score, part_fuzzy_score * 0.4)

            filename_scores[snippet.denotation] = max_score

        filename_scores_list.append(filename_scores)

    return filename_scores_list

# @file_cache(ignore_params=["snippets"])
def compute_vector_search_scores(queries: list[str], snippets: list[Snippet], cache_type: str = "main"):
    # get get dict of snippet to score
    with Timer() as timer:
        snippet_str_to_contents = {}
        for snippet in snippets:
            content = ""
            if hasattr(snippet, 'summary_zh') and snippet.summary_zh:
                content += f"//{snippet.summary_zh}\n"
            if hasattr(snippet, 'summary_en') and snippet.summary_en:
                content += f"//{snippet.summary_en}\n"
            if hasattr(snippet, 'contextual_info') and snippet.contextual_info:
                content += f"//{snippet.contextual_info}\n"
            content += snippet.get_snippet(add_ellipsis=False, add_lines=False)

            file_path_for_embedding = snippet.original_path if hasattr(snippet, 'original_path') and snippet.original_path else snippet.file_path

            snippet_str_to_contents[snippet.denotation] = SNIPPET_FORMAT.format(
                file_path=file_path_for_embedding,
                contents=content
            )
    logger.info(f"Snippet to contents took {timer.time_elapsed:.2f} seconds")
    snippet_contents_array = list(snippet_str_to_contents.values())
    multi_query_snippet_similarities = multi_get_query_texts_similarity(
        queries, snippet_contents_array, cache_type
    )
    snippet_denotations = [snippet.denotation for snippet in snippets]
    snippet_denotation_to_scores = [{
        snippet_denotations[i]: score
        for i, score in enumerate(query_snippet_similarities)
    } for query_snippet_similarities in multi_query_snippet_similarities]
    return snippet_denotation_to_scores

def get_lexical_cache_key(
    repo_directory: str,
    commit_hash: str | None = None,
    seed: str = "",
):
    commit_hash = commit_hash or subprocess.run(["git", "rev-parse", "HEAD"], cwd=repo_directory, capture_output=True, text=True).stdout.strip()
    repo_directory = os.path.basename(repo_directory)
    return f"{repo_directory}_{commit_hash}_{CACHE_VERSION}_{seed}"

@streamable
def prepare_lexical_search_index(
    repo_directory: str,
    sweep_config: SweepConfig,
    do_not_use_file_cache: bool = False, # choose to not cache results
    seed: str = "", # used for lexical cache key
    llm_summary: bool = False, # 是否使用LLM生成代码摘要
    enable_contextual_retrieval: bool = False, # 是否启用上下文检索
    force_reindex: bool = False, # 是否强制重新索引
    file_changes = None # 文件变化信息，用于增量更新
):
    lexical_cache_key = get_lexical_cache_key(repo_directory, seed=seed)

    yield "Collecting snippets...", [], None

    # 根据种子值选择对应的缓存
    cache_type_enum = CacheType.EXTENSION if seed == EXTENSION_SEED else CacheType.MAIN
    current_snippets_cache = get_snippets_cache(cache_type_enum.value)

    snippets_results = current_snippets_cache.get(lexical_cache_key)
    if snippets_results is None or force_reindex:
        snippets, file_list = directory_to_chunks(
            repo_directory,
            sweep_config,
            do_not_use_file_cache=do_not_use_file_cache,
            cache_type=cache_type_enum.value,
            enable_contextual_retrieval=enable_contextual_retrieval,
            file_changes=None  # 全量重建时不传递文件变化信息
        )
        current_snippets_cache[lexical_cache_key] = snippets, file_list
    elif file_changes and file_changes.has_changes:
        # 增量更新
        logger.info("执行增量更新，合并新旧代码片段...")
        cached_snippets, cached_file_list = snippets_results

        # 创建一个修改后的file_changes，只包含需要重新处理的文件（新增+修改）
        from utils.file_metadata import FileChangeResult
        files_to_process = FileChangeResult(
            added=file_changes.added,
            modified=file_changes.modified,
            deleted=[],  # 删除的文件不需要重新处理
            unchanged=file_changes.unchanged
        )

        # 获取需要重新处理文件的新snippets
        new_snippets, new_file_list = directory_to_chunks(
            repo_directory,
            sweep_config,
            do_not_use_file_cache=do_not_use_file_cache,
            cache_type=cache_type_enum.value,
            enable_contextual_retrieval=enable_contextual_retrieval,
            file_changes=files_to_process
        )

        # 合并snippets：移除所有变化文件的snippets（包括删除的），添加新的snippets
        all_changed_files_abs = set()
        for rel_path in file_changes.changed_files:  # 这里包含了删除的文件
            abs_path = os.path.join(repo_directory, rel_path)
            all_changed_files_abs.add(abs_path)

        # 保留未变化文件的snippets
        retained_snippets = [
            snippet for snippet in cached_snippets
            if snippet.file_path not in all_changed_files_abs
        ]

        # 合并snippets
        snippets = retained_snippets + new_snippets

        # 更新文件列表：移除所有变化的文件，添加新处理的文件
        retained_files = [f for f in cached_file_list if f not in all_changed_files_abs]
        file_list = retained_files + new_file_list

        logger.info(f"增量更新完成：保留 {len(retained_snippets)} 个旧片段，新增 {len(new_snippets)} 个片段，移除 {len(file_changes.deleted)} 个删除文件的片段")

        # 更新缓存
        current_snippets_cache[lexical_cache_key] = snippets, file_list
    else:
        snippets, file_list = snippets_results

    # 如果启用了LLM摘要生成，在构建索引前先生成摘要
    if llm_summary:
        # 检查snippets是否已经包含摘要
        snippets_already_have_summary = any(
            hasattr(snippet, 'summary_zh') and snippet.summary_zh
            for snippet in snippets[:5]  # 只检查前5个snippet就够了
        )

        if snippets_already_have_summary:
            logger.info("检测到snippets已包含摘要，跳过摘要生成步骤")
        else:
            from api.code_summary import generate_code_summaries

            yield "Generating code summaries...", snippets, None
            logger.info("开始使用LLM生成代码片段摘要...")

            # 为所有代码片段初始化空摘要，确保索引对应
            for snippet in snippets:
                if not hasattr(snippet, 'summary_zh'):
                    snippet.summary_zh = ""
                if not hasattr(snippet, 'summary_en'):
                    snippet.summary_en = ""

            # 准备代码片段数据
            snippet_data = []
            for i, snippet in enumerate(snippets):
                content = snippet.get_snippet(add_ellipsis=False, add_lines=False)
                snippet_data.append((content, snippet.file_path, i))

            # 使用code_summary模块生成摘要
            all_summaries = generate_code_summaries(snippet_data)

            # 将摘要添加到对应的代码片段，使用返回的原始索引确保一一对应
            for summary_zh, summary_en, original_idx in all_summaries:
                if 0 <= original_idx < len(snippets):
                    snippets[original_idx].summary_zh = summary_zh
                    snippets[original_idx].summary_en = summary_en
                else:
                    logger.warning(f"摘要索引超出范围: {original_idx}, snippets总数: {len(snippets)}")

            # 统计成功生成摘要的数量
            success_count = sum(1 for snippet in snippets if snippet.summary_zh or snippet.summary_en)
            logger.info(f"摘要生成完成，共为 {success_count}/{len(snippets)} 个代码片段生成摘要")

            # 重新缓存包含摘要的snippets
            current_snippets_cache[lexical_cache_key] = snippets, file_list

    # 如果启用了上下文检索，生成上下文信息
    if enable_contextual_retrieval:
        # 检查snippets是否已经包含上下文信息
        snippets_already_have_context = any(
            hasattr(snippet, 'contextual_info') and snippet.contextual_info
            for snippet in snippets[:5]  # 只检查前5个snippet就够了
        )

        if snippets_already_have_context:
            logger.info("检测到snippets已包含上下文信息，跳过上下文生成步骤")
        else:
            from api.contextual_retrieval import generate_contextual_info_for_snippets

            yield "Generating contextual information...", snippets, None
            logger.info("开始使用LLM生成代码片段上下文信息...")

            # 为所有代码片段初始化空上下文信息，确保索引对应
            for snippet in snippets:
                if not hasattr(snippet, 'contextual_info'):
                    snippet.contextual_info = ""

            # 生成上下文信息
            snippets = generate_contextual_info_for_snippets(snippets)

            # 统计成功生成上下文信息的数量
            success_count = sum(1 for snippet in snippets if snippet.contextual_info)
            logger.info(f"上下文信息生成完成，共为 {success_count}/{len(snippets)} 个代码片段生成上下文信息")

            # 重新缓存包含上下文信息的snippets
            current_snippets_cache[lexical_cache_key] = snippets, file_list

    yield "Building index...", snippets, None

    # 根据种子判断是否为扩展源，使用不同的缓存路径
    cache_path = get_lexical_index_cache_path(lexical_cache_key, cache_type_enum)

    index = prepare_index_from_snippets(
        snippets,
        len_repo_cache_dir=len(repo_directory) + 1,
        do_not_use_file_cache=do_not_use_file_cache,
        cache_path=cache_path,
        cache_type=cache_type_enum.value
    )

    yield "Lexical index built.", snippets, index

    return snippets, index


if __name__ == "__main__":
    repo_directory = os.getenv("REPO_DIRECTORY")
    sweep_config = SweepConfig()
    assert repo_directory
    import time
    start = time.time()
    # 添加llm_summary参数，默认为False
    _, index = prepare_lexical_search_index(
        repo_directory,
        sweep_config,
        do_not_use_file_cache=False,
        llm_summary=False
    )
    result = search_index("logger export", index)
    print("Time taken:", time.time() - start)
    # print some of the keys
    print(list(result.keys())[:5])
    # print the first 2 result keys sorting by value
    print(sorted(result.items(), key=lambda x: result.get(x, 0), reverse=True)[:5])
