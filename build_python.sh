#!/bin/bash
set -e

# 创建临时虚拟环境
python -m venv .venv
source .venv/bin/activate

# 安装所需的依赖
# echo "安装依赖..."
# pip install -r requirements.txt
# pip install pyinstaller -i https://mirrors.aliyun.com/pypi/simple
# pip install poetry -i https://mirrors.aliyun.com/pypi/simple

# 执行打包命令 (onedir版本)
poetry run pyinstaller -y --clean codemind_onefile.spec --distpath ./python_dist/macos/arm64

deactivate
echo "打包结果位于:"
echo ": ./python_dist/macos/arm64/codemind"
