image: docker-reg.devops.xiaohongshu.com/fe/fe-ci:$FE_CI_IMAGE_NODE18

before_script:
  - node -v
  - npm -v
  - npm i -g typescript@5.2.2
  - tsc -v
  - export CYPRESS_CACHE_FOLDER=/root/.cache/Cypress
  # - pnpm config set store-dir .pnpm-store
  - git config --global url."https://oauth2:<EMAIL>".insteadof "https://code.devops.xiaohongshu.com"

# 参考多任务平行执行
# https://github.com/cypress-io/cypress-example-kitchensink/blob/master/.gitlab-ci.yml
# - 上传 test report 在Pipeline 里面，MR 会和 main 分支diff test 结果
# - 图片 screenshot 会在测试失败截图
# 更多参考：
# - https://docs.cypress.io/guides/guides/screenshots-and-videos
# - https://docs.cypress.io/guides/tooling/visual-testing#Best-practices

.cache: &common_cache
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .pnpm-store
  policy: pull-push


stages:
  - deploy


release:beta:
  stage: deploy
  when: manual
  # cache:
  #   <<: *common_cache
  #   policy: pull
  except:
    - master
  script:
    - npm run build
    - npm publish --no-git-checks

release:
  stage: deploy
  cache:
    <<: *common_cache
    policy: pull
  when: manual
  only:
    - master
    - release
  script:
    - npm publish --no-git-checks
  resource_group: release
