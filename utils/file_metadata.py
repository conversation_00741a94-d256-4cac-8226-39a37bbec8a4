"""
文件元数据管理模块

提供文件变化检测功能，支持增量索引更新。
"""

import os
import json
import hashlib
from typing import Dict, List, Set, Optional
from dataclasses import dataclass, asdict
from loguru import logger

from config.server import get_cache_directory


@dataclass
class FileMetadata:
    """文件元数据"""
    path: str
    size: int
    mtime: float
    content_hash: str

    def __eq__(self, other):
        if not isinstance(other, FileMetadata):
            return False
        return (self.size == other.size and
                self.mtime == other.mtime and
                self.content_hash == other.content_hash)


@dataclass
class FileChangeResult:
    """文件变化检测结果"""
    added: List[str]
    modified: List[str]
    deleted: List[str]
    unchanged: List[str]

    @property
    def has_changes(self) -> bool:
        """是否有文件变化"""
        return bool(self.added or self.modified or self.deleted)

    @property
    def changed_files(self) -> List[str]:
        """所有变化的文件（包括新增、修改和删除）"""
        return self.added + self.modified + self.deleted


def calculate_file_hash(file_path: str, chunk_size: int = 8192) -> str:
    """
    计算文件内容哈希

    参数:
        file_path: 文件路径
        chunk_size: 读取块大小

    返回:
        文件内容的MD5哈希值
    """
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except (IOError, OSError) as e:
        logger.warning(f"无法读取文件 {file_path}: {e}")
        return ""


def get_file_metadata(file_path: str) -> Optional[FileMetadata]:
    """
    获取文件元数据

    参数:
        file_path: 文件路径

    返回:
        文件元数据，如果文件不存在或无法访问则返回None
    """
    try:
        stat = os.stat(file_path)
        content_hash = calculate_file_hash(file_path)

        return FileMetadata(
            path=file_path,
            size=stat.st_size,
            mtime=stat.st_mtime,
            content_hash=content_hash
        )
    except (OSError, IOError) as e:
        logger.warning(f"无法获取文件元数据 {file_path}: {e}")
        return None


def scan_directory_metadata(directory: str, exclude_dirs: Optional[Set[str]] = None) -> Dict[str, FileMetadata]:
    """
    扫描目录下所有文件的元数据

    参数:
        directory: 目录路径
        exclude_dirs: 要排除的目录名集合

    返回:
        文件路径到元数据的映射
    """
    if exclude_dirs is None:
        exclude_dirs = {".git", "node_modules", ".venv", "build", "venv", "patch", "packages/blobs", "dist", "oa3gen"}

    metadata_dict = {}

    def should_exclude_dir(dir_name: str) -> bool:
        return dir_name in exclude_dirs

    def scan_recursive(current_dir: str):
        try:
            for entry in os.scandir(current_dir):
                if entry.is_dir(follow_symlinks=False):
                    if not should_exclude_dir(entry.name):
                        scan_recursive(entry.path)
                elif entry.is_file(follow_symlinks=False):
                    metadata = get_file_metadata(entry.path)
                    if metadata:
                        # 使用相对路径作为键
                        rel_path = os.path.relpath(entry.path, directory)
                        metadata_dict[rel_path] = metadata
        except (OSError, IOError) as e:
            logger.warning(f"扫描目录失败 {current_dir}: {e}")

    scan_recursive(directory)
    return metadata_dict


def get_metadata_cache_path(repo_directory: str) -> str:
    """
    获取元数据缓存文件路径

    参数:
        repo_directory: 仓库目录路径

    返回:
        元数据缓存文件路径
    """
    # 使用仓库路径的哈希作为缓存文件名
    repo_hash = hashlib.md5(repo_directory.encode()).hexdigest()
    cache_dir = os.path.join(get_cache_directory(), "file_metadata")
    os.makedirs(cache_dir, exist_ok=True)
    return os.path.join(cache_dir, f"metadata_{repo_hash}.json")


def load_metadata_cache(repo_directory: str) -> Dict[str, FileMetadata]:
    """
    加载元数据缓存

    参数:
        repo_directory: 仓库目录路径

    返回:
        缓存的文件元数据字典
    """
    cache_path = get_metadata_cache_path(repo_directory)

    if not os.path.exists(cache_path):
        return {}

    try:
        with open(cache_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 将字典转换为FileMetadata对象
        metadata_dict = {}
        for rel_path, metadata_data in data.items():
            metadata_dict[rel_path] = FileMetadata(**metadata_data)

        return metadata_dict
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.warning(f"加载元数据缓存失败 {cache_path}: {e}")
        return {}


def save_metadata_cache(repo_directory: str, metadata_dict: Dict[str, FileMetadata]):
    """
    保存元数据缓存

    参数:
        repo_directory: 仓库目录路径
        metadata_dict: 文件元数据字典
    """
    cache_path = get_metadata_cache_path(repo_directory)

    try:
        # 将FileMetadata对象转换为字典
        data = {}
        for rel_path, metadata in metadata_dict.items():
            data[rel_path] = asdict(metadata)

        with open(cache_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logger.debug(f"元数据缓存已保存到 {cache_path}")
    except (IOError, OSError) as e:
        logger.error(f"保存元数据缓存失败 {cache_path}: {e}")


def detect_file_changes(repo_directory: str) -> FileChangeResult:
    """
    检测文件变化

    参数:
        repo_directory: 仓库目录路径

    返回:
        文件变化检测结果
    """
    logger.info(f"开始检测文件变化: {repo_directory}")

    # 加载上次的元数据缓存
    cached_metadata = load_metadata_cache(repo_directory)
    is_first_run = len(cached_metadata) == 0

    # 扫描当前目录的元数据
    current_metadata = scan_directory_metadata(repo_directory)

    # 如果是首次运行，将所有文件标记为未变化（假设已有索引缓存）
    if is_first_run:
        logger.info("首次运行文件变化检测，将检查是否存在索引缓存")
        result = FileChangeResult(
            added=[],
            modified=[],
            deleted=[],
            unchanged=list(current_metadata.keys())
        )
    else:
        # 分析变化
        cached_files = set(cached_metadata.keys())
        current_files = set(current_metadata.keys())

        added = list(current_files - cached_files)
        deleted = list(cached_files - current_files)

        # 检查修改的文件
        modified = []
        unchanged = []

        for rel_path in current_files & cached_files:
            current_meta = current_metadata[rel_path]
            cached_meta = cached_metadata[rel_path]

            if current_meta != cached_meta:
                modified.append(rel_path)
            else:
                unchanged.append(rel_path)

        result = FileChangeResult(
            added=sorted(added),
            modified=sorted(modified),
            deleted=sorted(deleted),
            unchanged=sorted(unchanged)
        )

    logger.info(f"文件变化检测完成: 新增{len(result.added)}, 修改{len(result.modified)}, 删除{len(result.deleted)}, 未变化{len(result.unchanged)}")

    # 保存当前元数据作为新的缓存
    save_metadata_cache(repo_directory, current_metadata)

    return result


def clear_metadata_cache(repo_directory: str):
    """
    清除元数据缓存

    参数:
        repo_directory: 仓库目录路径
    """
    cache_path = get_metadata_cache_path(repo_directory)
    try:
        if os.path.exists(cache_path):
            os.remove(cache_path)
            logger.info(f"已清除元数据缓存: {cache_path}")
    except OSError as e:
        logger.error(f"清除元数据缓存失败 {cache_path}: {e}")
