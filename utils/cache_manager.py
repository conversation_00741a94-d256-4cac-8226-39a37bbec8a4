"""
统一缓存管理器

提供统一的缓存访问接口，替代分散在各个文件中的缓存选择函数。
"""

import os
from typing import Optional
from diskcache import Cache
from loguru import logger

from config.server import get_cache_directory
from config.cache_constants import (
    CacheType, CacheNames, normalize_cache_type, 
    get_cache_dir_name, EXTENSION_CACHE_BASE_DIR
)


class CacheManager:
    """
    统一缓存管理器
    
    管理所有类型的缓存实例，提供统一的访问接口。
    """
    
    def __init__(self):
        """初始化缓存管理器"""
        self._main_caches = {}
        self._extension_caches = {}
        self._last_cache_directory = None
        self._initialize_caches()
    
    def _initialize_caches(self):
        """初始化所有缓存实例"""
        current_cache_directory = get_cache_directory()

        # 如果缓存目录没有变化，不需要重新初始化
        if self._last_cache_directory == current_cache_directory and self._main_caches:
            return

        self._last_cache_directory = current_cache_directory

        # 确保扩展源缓存目录存在
        extension_cache_dir = os.path.join(current_cache_directory, EXTENSION_CACHE_BASE_DIR)
        os.makedirs(extension_cache_dir, exist_ok=True)

        # 初始化主仓库缓存
        for cache_name in CacheNames:
            cache_dir_name = get_cache_dir_name(cache_name)
            main_cache_path = os.path.join(current_cache_directory, cache_dir_name)
            extension_cache_path = os.path.join(extension_cache_dir, cache_dir_name)

            try:
                self._main_caches[cache_name] = Cache(main_cache_path)
                self._extension_caches[cache_name] = Cache(extension_cache_path)
            except Exception as e:
                logger.error(f"初始化缓存失败: {cache_name.value}, 错误: {e}")
                # 创建空的缓存实例作为fallback
                self._main_caches[cache_name] = None
                self._extension_caches[cache_name] = None
    
    def get_cache(self, cache_name: CacheNames, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """
        获取指定类型的缓存实例

        参数:
            cache_name: 缓存名称
            cache_type: 缓存类型

        返回:
            缓存实例，如果获取失败返回 None
        """
        try:
            # 检查是否需要重新初始化缓存
            self._initialize_caches()

            cache_type = normalize_cache_type(cache_type)

            if cache_type == CacheType.EXTENSION:
                return self._extension_caches.get(cache_name)
            else:
                return self._main_caches.get(cache_name)
        except Exception as e:
            logger.warning(f"获取缓存失败: {cache_name.value}, 类型: {cache_type}, 错误: {e}")
            return None
    
    def get_vector_cache(self, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """获取向量缓存"""
        return self.get_cache(CacheNames.VECTOR, cache_type)
    
    def get_snippets_cache(self, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """获取代码片段缓存"""
        return self.get_cache(CacheNames.SNIPPETS, cache_type)
    
    def get_token_cache(self, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """获取分词缓存"""
        return self.get_cache(CacheNames.TOKEN, cache_type)
    
    def get_chunk_cache(self, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """获取代码分块缓存"""
        return self.get_cache(CacheNames.CHUNK, cache_type)
    
    def get_file_name_cache(self, cache_type: CacheType = CacheType.MAIN) -> Optional[Cache]:
        """获取文件名缓存"""
        return self.get_cache(CacheNames.FILE_NAME, cache_type)
    
    def clear_cache(self, cache_name: CacheNames, cache_type: CacheType = CacheType.MAIN):
        """
        清理指定缓存
        
        参数:
            cache_name: 缓存名称
            cache_type: 缓存类型
        """
        try:
            cache = self.get_cache(cache_name, cache_type)
            if cache:
                cache.clear()
                logger.info(f"已清理缓存: {cache_name.value}, 类型: {cache_type.value}")
        except Exception as e:
            logger.error(f"清理缓存失败: {cache_name.value}, 类型: {cache_type.value}, 错误: {e}")
    
    def clear_all_caches(self, cache_type: CacheType = CacheType.MAIN):
        """
        清理指定类型的所有缓存
        
        参数:
            cache_type: 缓存类型
        """
        for cache_name in CacheNames:
            self.clear_cache(cache_name, cache_type)


# 全局缓存管理器实例 - 延迟初始化
_cache_manager = None

def get_cache_manager():
    """获取缓存管理器实例，支持延迟初始化"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager


# 向后兼容的函数接口
def get_vector_cache(cache_type: str = "main") -> Optional[Cache]:
    """向后兼容的向量缓存获取函数"""
    return get_cache_manager().get_vector_cache(normalize_cache_type(cache_type))


def get_snippets_cache(cache_type: str = "main") -> Optional[Cache]:
    """向后兼容的代码片段缓存获取函数"""
    return get_cache_manager().get_snippets_cache(normalize_cache_type(cache_type))


def get_token_cache(cache_type: str = "main") -> Optional[Cache]:
    """向后兼容的分词缓存获取函数"""
    return get_cache_manager().get_token_cache(normalize_cache_type(cache_type))


def get_chunk_cache(cache_type: str = "main") -> Optional[Cache]:
    """向后兼容的代码分块缓存获取函数"""
    return get_cache_manager().get_chunk_cache(normalize_cache_type(cache_type))


def get_file_name_cache(cache_type: str = "main") -> Optional[Cache]:
    """向后兼容的文件名缓存获取函数"""
    return get_cache_manager().get_file_name_cache(normalize_cache_type(cache_type))
