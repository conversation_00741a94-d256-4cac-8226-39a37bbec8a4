import json
import requests
import backoff
from loguru import logger
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from logn.cache import file_cache
from config.server import CODEWIZ_RERANK_URL

@dataclass
class RerankResult:
    """Represents a single rerank result."""
    document: str
    index: int
    relevance_score: float

@dataclass
class RerankResponse:
    """Represents the response from the rerank API."""
    results: List[RerankResult]

@backoff.on_exception(
    backoff.expo,
    Exception,
    max_tries=3,
    jitter=backoff.random_jitter,
)
@file_cache()
def gte_rerank_call(
    query: str,
    documents: list[str],
    normalize: bool = True,
    **kwargs,
):
    """
    调用GTE rerank API对文档进行重排序

    Args:
        query: 查询文本
        documents: 文档列表
        normalize: 是否将分数归一化到0-1范围

    Returns:
        RerankResponse对象，包含重排序结果
    """
    url = CODEWIZ_RERANK_URL
    headers = {'Content-Type': 'application/json'}

    # 构建text_pairs
    text_pairs = []
    for doc in documents:
        text_pairs.append({
            "query": query,
            "passage": doc
        })

    payload = {
        "text_pairs": text_pairs,
        "normalize": normalize
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()  # 如果请求失败，抛出异常

        # 解析响应
        response_json = response.json()

        # 从新的响应格式中提取数据
        if "code" in response_json and response_json["code"] == 0 and "data" in response_json:
            # 新的响应格式: {"code":0,"msg":"","cost_time":0.03,"data":[{"query":"...","passage":"...","score":0.831964}]}
            rerank_data = response_json["data"]

            # 创建RerankResult对象列表
            results = []

            # 创建文档到索引的映射，以便正确匹配响应中的passage和原始文档
            doc_to_index = {doc: i for i, doc in enumerate(documents)}

            for item in rerank_data:
                passage = item["passage"]
                if passage in doc_to_index:
                    idx = doc_to_index[passage]
                    results.append(RerankResult(
                        document=documents[idx],
                        index=idx,
                        relevance_score=float(item["score"])
                    ))
                else:
                    # 如果找不到精确匹配，则按顺序处理
                    logger.warning(f"无法在原始文档中找到精确匹配的passage: {passage[:50]}...")

            # 如果没有找到任何匹配，则回退到按顺序处理
            if not results and rerank_data:
                logger.warning("无法匹配任何文档，回退到按顺序处理")
                for i, item in enumerate(rerank_data):
                    if i < len(documents):
                        results.append(RerankResult(
                            document=documents[i],
                            index=i,
                            relevance_score=float(item["score"])
                        ))
        else:
            # 兼容旧格式，直接使用响应作为分数列表
            scores = response_json
            results = []
            for i, score in enumerate(scores):
                results.append(RerankResult(
                    document=documents[i],
                    index=i,
                    relevance_score=float(score)
                ))

        # 确保结果不为空
        if not results:
            logger.warning("未能从响应中提取有效的重排序结果，返回原始文档顺序")
            for i, doc in enumerate(documents):
                results.append(RerankResult(
                    document=doc,
                    index=i,
                    relevance_score=1.0 - (i * 0.01)  # 简单的降序分数
                ))

        # 按相关性分数降序排序
        results.sort(key=lambda x: x.relevance_score, reverse=True)

        return RerankResponse(results=results)
    except Exception as e:
        logger.error(f"GTE rerank failed: {e}")
        raise e
