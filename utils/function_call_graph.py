"""
函数调用图构建模块

此模块用于分析代码库中的函数定义和调用关系，构建函数级别的调用图。
支持JavaScript/TypeScript和Python等语言。
"""

import os
import ast
import json
import networkx as nx
from typing import Dict, List, Tuple, Set, Optional, Any
from pathlib import Path
import tree_sitter_languages
from tree_sitter import Node, Parser, Tree
import logging

logger = logging.getLogger(__name__)

class FunctionInfo:
    """函数信息类"""
    def __init__(self, name: str, file_path: str, start_line: int, end_line: int, 
                 params: List[str] = None, is_exported: bool = False):
        self.name = name
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.params = params or []
        self.is_exported = is_exported
        
    def __str__(self):
        return f"{self.file_path}:{self.name}({self.start_line}-{self.end_line})"
    
    def to_dict(self):
        return {
            'name': self.name,
            'file_path': self.file_path,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'params': self.params,
            'is_exported': self.is_exported
        }

class FunctionCall:
    """函数调用信息类"""
    def __init__(self, caller_function: str, caller_file: str, caller_line: int,
                 called_function: str, called_file: str = None):
        self.caller_function = caller_function
        self.caller_file = caller_file
        self.caller_line = caller_line
        self.called_function = called_function
        self.called_file = called_file  # 可能为None，需要通过import分析确定
        
    def __str__(self):
        called_location = f"{self.called_file}:{self.called_function}" if self.called_file else self.called_function
        return f"{self.caller_file}:{self.caller_function}({self.caller_line}) -> {called_location}"

class FunctionCallGraphBuilder:
    """函数调用图构建器"""
    
    def __init__(self, repo_directory: str):
        self.repo_directory = repo_directory
        self.functions: Dict[str, List[FunctionInfo]] = {}  # function_name -> [FunctionInfo]
        self.function_calls: List[FunctionCall] = []
        self.import_map: Dict[str, Dict[str, str]] = {}  # file_path -> {imported_name: source_file}
        self.call_graph = nx.DiGraph()
        
    def build_graph(self) -> nx.DiGraph:
        """构建完整的函数调用图"""
        logger.info(f"开始构建函数调用图: {self.repo_directory}")
        
        # 1. 扫描所有文件，提取函数定义和调用
        self._scan_files()
        
        # 2. 解析import关系
        self._resolve_imports()
        
        # 3. 构建调用图
        self._build_call_graph()
        
        logger.info(f"函数调用图构建完成，包含 {len(self.call_graph.nodes)} 个节点，{len(self.call_graph.edges)} 条边")
        return self.call_graph
    
    def _scan_files(self):
        """扫描所有代码文件"""
        for root, dirs, files in os.walk(self.repo_directory):
            # 跳过常见的忽略目录
            dirs[:] = [d for d in dirs if d not in ['node_modules', '.git', 'build', 'dist', '__pycache__', '.venv', 'venv', 'patch', 'packages/blobs', 'dist', 'oa3gen']]            
            for file in files:
                if file.endswith(('.ts', '.tsx', '.js', '.jsx', '.py')):
                    file_path = os.path.join(root, file)
                    try:
                        self._analyze_file(file_path)
                    except Exception as e:
                        logger.warning(f"分析文件失败 {file_path}: {e}")
    
    def _analyze_file(self, file_path: str):
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            logger.warning(f"读取文件失败 {file_path}: {e}")
            return
            
        # 根据文件扩展名选择分析方法
        if file_path.endswith('.py'):
            self._analyze_python_file(file_path, content)
        elif file_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
            self._analyze_typescript_file(file_path, content)
    
    def _analyze_python_file(self, file_path: str, content: str):
        """分析Python文件"""
        try:
            tree = ast.parse(content)
            
            # 提取函数定义
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = FunctionInfo(
                        name=node.name,
                        file_path=file_path,
                        start_line=node.lineno,
                        end_line=getattr(node, 'end_lineno', node.lineno),
                        params=[arg.arg for arg in node.args.args]
                    )
                    
                    if node.name not in self.functions:
                        self.functions[node.name] = []
                    self.functions[node.name].append(func_info)
                    
                    # 提取函数内的调用
                    self._extract_python_calls(node, file_path, node.name)
                    
        except SyntaxError as e:
            logger.warning(f"Python语法错误 {file_path}: {e}")
    
    def _extract_python_calls(self, func_node: ast.FunctionDef, file_path: str, caller_name: str):
        """提取Python函数中的调用"""
        for node in ast.walk(func_node):
            if isinstance(node, ast.Call):
                called_name = None
                if isinstance(node.func, ast.Name):
                    called_name = node.func.id
                elif isinstance(node.func, ast.Attribute):
                    called_name = node.func.attr
                
                if called_name:
                    call = FunctionCall(
                        caller_function=caller_name,
                        caller_file=file_path,
                        caller_line=node.lineno,
                        called_function=called_name
                    )
                    self.function_calls.append(call)

    def _analyze_typescript_file(self, file_path: str, content: str):
        """分析TypeScript/JavaScript文件"""
        try:
            # 使用tree-sitter解析TypeScript
            parser = Parser()
            if file_path.endswith(('.ts', '.tsx')):
                language = tree_sitter_languages.get_language("typescript")
            else:
                language = tree_sitter_languages.get_language("javascript")
            parser.set_language(language)
            tree = parser.parse(bytes(content, "utf8"))

            # 提取函数定义和调用
            self._extract_ts_functions(tree.root_node, file_path, content)

        except Exception as e:
            # 如果tree-sitter解析失败，尝试使用正则表达式作为备选方案
            self._extract_ts_functions_regex(file_path, content)

    def _extract_ts_functions(self, node: Node, file_path: str, content: str):
        """提取TypeScript函数定义和调用"""
        lines = content.split('\n')

        def traverse(node: Node, current_function: str = None):
            # 检查是否是函数定义
            if node.type in ['function_declaration', 'method_definition', 'arrow_function']:
                func_name = self._get_function_name(node, lines)
                if func_name:
                    start_line = node.start_point[0] + 1
                    end_line = node.end_point[0] + 1

                    func_info = FunctionInfo(
                        name=func_name,
                        file_path=file_path,
                        start_line=start_line,
                        end_line=end_line,
                        is_exported=self._is_exported_function(node, lines)
                    )

                    if func_name not in self.functions:
                        self.functions[func_name] = []
                    self.functions[func_name].append(func_info)

                    # 递归分析函数体，查找调用
                    for child in node.children:
                        traverse(child, func_name)
                    return

            # 检查是否是函数调用
            if node.type == 'call_expression' and current_function:
                called_name = self._get_called_function_name(node, lines)
                if called_name:
                    call = FunctionCall(
                        caller_function=current_function,
                        caller_file=file_path,
                        caller_line=node.start_point[0] + 1,
                        called_function=called_name
                    )
                    self.function_calls.append(call)

            # 递归处理子节点
            for child in node.children:
                traverse(child, current_function)

        traverse(node)

    def _get_function_name(self, node: Node, lines: List[str]) -> Optional[str]:
        """获取函数名"""
        try:
            if node.type == 'function_declaration':
                # 查找identifier子节点
                for child in node.children:
                    if child.type == 'identifier':
                        start_byte = child.start_byte
                        end_byte = child.end_byte
                        return '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
            elif node.type == 'method_definition':
                # 查找property_name
                for child in node.children:
                    if child.type == 'property_name':
                        start_byte = child.start_byte
                        end_byte = child.end_byte
                        return '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
            elif node.type == 'arrow_function':
                # 对于箭头函数，需要查看父节点是否是变量声明
                parent = node.parent
                if parent and parent.type == 'variable_declarator':
                    for child in parent.children:
                        if child.type == 'identifier':
                            start_byte = child.start_byte
                            end_byte = child.end_byte
                            return '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
        except Exception:
            pass
        return None

    def _get_called_function_name(self, node: Node, lines: List[str]) -> Optional[str]:
        """获取被调用的函数名"""
        try:
            # call_expression的第一个子节点通常是被调用的函数
            for child in node.children:
                if child.type == 'identifier':
                    start_byte = child.start_byte
                    end_byte = child.end_byte
                    return '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
                elif child.type == 'member_expression':
                    # 对于obj.method()形式的调用，获取method名
                    for grandchild in child.children:
                        if grandchild.type == 'property_identifier':
                            start_byte = grandchild.start_byte
                            end_byte = grandchild.end_byte
                            return '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
        except Exception:
            pass
        return None

    def _is_exported_function(self, node: Node, lines: List[str]) -> bool:
        """检查函数是否被导出"""
        try:
            # 检查节点前是否有export关键字
            parent = node.parent
            while parent:
                if parent.type == 'export_statement':
                    return True
                parent = parent.parent

            # 检查同一行是否有export关键字
            line_content = lines[node.start_point[0]].strip()
            return line_content.startswith('export')
        except Exception:
            pass
        return False

    def _extract_ts_functions_regex(self, file_path: str, content: str):
        """使用正则表达式提取TypeScript/JavaScript函数（备选方案）"""
        import re

        lines = content.split('\n')

        # 首先解析import语句，建立别名映射
        import_aliases = {}  # local_name -> original_name
        for line in lines:
            # 解析 import { validate as feValidate } from '../../utils/validate'
            alias_match = re.search(r'import\s*\{\s*(\w+)\s+as\s+(\w+)\s*\}\s*from\s*[\'"]([^\'"]+)[\'"]', line)
            if alias_match:
                original_name, alias_name, import_path = alias_match.groups()
                import_aliases[alias_name] = original_name

                # 同时建立import_map映射
                resolved_path = self._resolve_import_path(file_path, import_path)
                if file_path not in self.import_map:
                    self.import_map[file_path] = {}
                self.import_map[file_path][alias_name] = f"{original_name}@{resolved_path}"

            # 解析 import { functionName } from '...'
            import_match = re.search(r'import\s*\{\s*([^}]+)\s*\}\s*from\s*[\'"]([^\'"]+)[\'"]', line)
            if import_match:
                imports_str, import_path = import_match.groups()
                resolved_path = self._resolve_import_path(file_path, import_path)

                if file_path not in self.import_map:
                    self.import_map[file_path] = {}

                # 处理多个导入
                for item in imports_str.split(','):
                    item = item.strip()
                    if ' as ' in item:
                        original, alias = item.split(' as ')
                        alias_name = alias.strip()
                        original_name = original.strip()
                        import_aliases[alias_name] = original_name
                        self.import_map[file_path][alias_name] = f"{original_name}@{resolved_path}"
                    else:
                        self.import_map[file_path][item] = f"{item}@{resolved_path}"

        # 函数定义的正则表达式模式
        function_patterns = [
            # function functionName() {}
            r'^\s*(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(',
            # const functionName = () => {}
            r'^\s*(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\(',
            # let functionName = () => {}
            r'^\s*(?:export\s+)?let\s+(\w+)\s*=\s*(?:async\s+)?\(',
            # var functionName = function() {}
            r'^\s*(?:export\s+)?var\s+(\w+)\s*=\s*(?:async\s+)?function',
            # functionName: function() {}
            r'^\s*(\w+)\s*:\s*(?:async\s+)?function\s*\(',
            # functionName() {}
            r'^\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{',
        ]

        # 函数调用的正则表达式模式
        call_patterns = [
            # functionName()
            r'(\w+)\s*\(',
            # await functionName()
            r'await\s+(\w+)\s*\(',
            # const result = await functionName()
            r'=\s*await\s+(\w+)\s*\(',
            # const result = functionName()
            r'=\s*(\w+)\s*\(',
        ]

        current_function = None

        for line_num, line in enumerate(lines, 1):
            # 检查函数定义
            for pattern in function_patterns:
                match = re.search(pattern, line)
                if match:
                    func_name = match.group(1)
                    if func_name and not func_name in ['if', 'for', 'while', 'switch', 'catch']:
                        # 估算函数结束行（简单实现）
                        end_line = self._estimate_function_end_line(lines, line_num - 1)

                        func_info = FunctionInfo(
                            name=func_name,
                            file_path=file_path,
                            start_line=line_num,
                            end_line=end_line,
                            is_exported='export' in line
                        )

                        if func_name not in self.functions:
                            self.functions[func_name] = []
                        self.functions[func_name].append(func_info)
                        current_function = func_name
                        break

            # 检查函数调用（只在函数内部）
            if current_function:
                for pattern in call_patterns:
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        called_name = match.group(1)
                        if called_name and called_name != current_function:
                            # 过滤掉一些明显不是函数的关键字
                            if called_name not in ['if', 'for', 'while', 'switch', 'catch', 'return', 'throw', 'new', 'typeof', 'instanceof']:
                                # 对于函数调用，我们需要区分是调用本地函数还是导入函数
                                # 如果有别名映射，说明这是导入的函数，使用原始名称
                                # 如果没有别名映射，说明这是本地函数或直接导入的函数，使用调用名称

                                if called_name in import_aliases:
                                    # 这是一个别名调用，使用原始名称
                                    original_name = import_aliases[called_name]
                                    call = FunctionCall(
                                        caller_function=current_function,
                                        caller_file=file_path,
                                        caller_line=line_num,
                                        called_function=called_name  # 保持别名，让 _resolve_called_function_file 处理
                                    )
                                    self.function_calls.append(call)
                                else:
                                    # 这是一个直接调用，使用调用名称
                                    call = FunctionCall(
                                        caller_function=current_function,
                                        caller_file=file_path,
                                        caller_line=line_num,
                                        called_function=called_name
                                    )
                                    self.function_calls.append(call)

    def _estimate_function_end_line(self, lines: List[str], start_line_idx: int) -> int:
        """估算函数结束行（简单的大括号匹配）"""
        brace_count = 0
        in_function = False

        for i in range(start_line_idx, len(lines)):
            line = lines[i]

            # 计算大括号
            for char in line:
                if char == '{':
                    brace_count += 1
                    in_function = True
                elif char == '}':
                    brace_count -= 1
                    if in_function and brace_count == 0:
                        return i + 1

        # 如果没有找到匹配的大括号，返回一个估算值
        return min(start_line_idx + 20, len(lines))

    def _resolve_imports(self):
        """解析import关系"""
        logger.info("开始解析import关系")

        for root, dirs, files in os.walk(self.repo_directory):
            dirs[:] = [d for d in dirs if d not in ['node_modules', '.git', 'build', 'dist', '__pycache__']]

            for file in files:
                if file.endswith(('.ts', '.tsx', '.js', '.jsx', '.py')):
                    file_path = os.path.join(root, file)
                    try:
                        self._analyze_imports(file_path)
                    except Exception as e:
                        logger.warning(f"分析import失败 {file_path}: {e}")

    def _analyze_imports(self, file_path: str):
        """分析单个文件的import"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception:
            return

        self.import_map[file_path] = {}

        if file_path.endswith('.py'):
            self._analyze_python_imports(file_path, content)
        elif file_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
            self._analyze_typescript_imports(file_path, content)

    def _analyze_python_imports(self, file_path: str, content: str):
        """分析Python import"""
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        # 简化处理，假设同一目录下的模块
                        module_name = alias.name
                        self.import_map[file_path][alias.asname or alias.name] = module_name
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            self.import_map[file_path][alias.asname or alias.name] = node.module
        except SyntaxError:
            pass

    def _analyze_typescript_imports(self, file_path: str, content: str):
        """分析TypeScript import"""
        try:
            parser = Parser()
            language = tree_sitter_languages.get_language("typescript")
            parser.set_language(language)
            tree = parser.parse(bytes(content, "utf8"))

            lines = content.split('\n')

            def traverse(node: Node):
                if node.type == 'import_statement':
                    self._parse_ts_import(node, lines, file_path)

                for child in node.children:
                    traverse(child)

            traverse(tree.root_node)
        except Exception:
            # 如果tree-sitter失败，使用正则表达式备选方案
            self._analyze_typescript_imports_regex(file_path, content)

    def _parse_ts_import(self, node: Node, lines: List[str], file_path: str):
        """解析TypeScript import语句"""
        try:
            import_source = None
            imported_names = []

            for child in node.children:
                if child.type == 'string':
                    # 获取import源
                    start_byte = child.start_byte
                    end_byte = child.end_byte
                    import_source = '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8').strip('"\'')
                elif child.type == 'import_clause':
                    # 获取导入的名称
                    imported_names = self._extract_imported_names(child, lines)

            if import_source and imported_names:
                # 解析相对路径
                resolved_path = self._resolve_import_path(file_path, import_source)
                for name in imported_names:
                    self.import_map[file_path][name] = f"{name}@{resolved_path}"

        except Exception:
            pass

    def _extract_imported_names(self, node: Node, lines: List[str]) -> List[str]:
        """提取导入的名称"""
        names = []
        try:
            def traverse(n: Node):
                if n.type == 'identifier':
                    start_byte = n.start_byte
                    end_byte = n.end_byte
                    name = '\n'.join(lines).encode('utf-8')[start_byte:end_byte].decode('utf-8')
                    names.append(name)
                for child in n.children:
                    traverse(child)

            traverse(node)
        except Exception:
            pass
        return names

    def _resolve_import_path(self, current_file: str, import_source: str) -> str:
        """解析import路径"""
        if import_source.startswith('.'):
            # 相对路径
            current_dir = os.path.dirname(current_file)
            resolved = os.path.normpath(os.path.join(current_dir, import_source))

            # 尝试添加常见的文件扩展名
            for ext in ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx']:
                if os.path.exists(resolved + ext):
                    return resolved + ext

            return resolved
        else:
            # 绝对路径或node_modules
            return import_source

    def _build_call_graph(self):
        """构建调用图"""
        logger.info("开始构建调用图")

        # 为每个函数创建节点
        for func_name, func_infos in self.functions.items():
            for func_info in func_infos:
                node_id = f"{func_info.file_path}:{func_name}"
                self.call_graph.add_node(node_id, **func_info.to_dict())

        # 添加调用边
        for call in self.function_calls:
            caller_id = f"{call.caller_file}:{call.caller_function}"

            # 查找被调用函数的实际位置
            called_file = self._resolve_called_function_file(call)
            if called_file:
                called_id = f"{called_file}:{call.called_function}"
                if self.call_graph.has_node(caller_id) and self.call_graph.has_node(called_id):
                    # 避免自循环
                    if caller_id != called_id:
                        self.call_graph.add_edge(caller_id, called_id, line=call.caller_line)

    def _resolve_called_function_file(self, call: FunctionCall) -> Optional[str]:
        """解析被调用函数的文件位置"""
        # 1. 检查import映射（优先级最高）
        if call.caller_file in self.import_map:
            imports = self.import_map[call.caller_file]
            if call.called_function in imports:
                # 这是一个导入的函数，查找原始函数
                mapping = imports[call.called_function]

                # 检查是否是新的别名映射格式 "function_name@file_path"
                if "@" in mapping:
                    original_name, target_file = mapping.split("@", 1)
                    # 更新调用记录中的函数名为原始名称
                    call.called_function = original_name
                    return target_file
                else:
                    # 旧格式，直接查找函数
                    original_name = mapping
                    if original_name in self.functions:
                        for func_info in self.functions[original_name]:
                            # 确保不是同一个文件（避免自循环）
                            if func_info.file_path != call.caller_file:
                                return func_info.file_path

        # 2. 检查是否在同一文件中（但排除import的情况）
        # 如果函数调用没有通过import映射解析，则检查本地函数
        if call.called_function in self.functions:
            for func_info in self.functions[call.called_function]:
                if func_info.file_path == call.caller_file:
                    return func_info.file_path

        # 3. 在所有文件中查找导出的函数
        if call.called_function in self.functions:
            for func_info in self.functions[call.called_function]:
                if func_info.is_exported and func_info.file_path != call.caller_file:
                    return func_info.file_path

        return None

    def _analyze_typescript_imports_regex(self, file_path: str, content: str):
        """使用正则表达式分析TypeScript import（备选方案）"""
        import re

        lines = content.split('\n')

        for line in lines:
            # 匹配各种import模式
            patterns = [
                # import { validate as feValidate } from '../../utils/validate'
                r"import\s*\{\s*(\w+)\s+as\s+(\w+)\s*\}\s*from\s*['\"]([^'\"]+)['\"]",
                # import { validate } from '../../utils/validate'
                r"import\s*\{\s*([^}]+)\s*\}\s*from\s*['\"]([^'\"]+)['\"]",
                # import validate from '../../utils/validate'
                r"import\s+(\w+)\s+from\s*['\"]([^'\"]+)['\"]",
            ]

            for pattern in patterns:
                match = re.search(pattern, line.strip())
                if match:
                    if "as" in pattern:
                        # 处理别名导入
                        original_name, alias_name, import_path = match.groups()
                        resolved_path = self._resolve_import_path(file_path, import_path)

                        if file_path not in self.import_map:
                            self.import_map[file_path] = {}
                        # 别名映射：别名 -> 原始函数名@文件路径
                        self.import_map[file_path][alias_name] = f"{original_name}@{resolved_path}"

                    elif "{" in pattern and len(match.groups()) == 2:
                        # 处理命名导入
                        imports_str, import_path = match.groups()
                        resolved_path = self._resolve_import_path(file_path, import_path)

                        if file_path not in self.import_map:
                            self.import_map[file_path] = {}

                        # 解析多个导入
                        for item in imports_str.split(','):
                            item = item.strip()
                            if ' as ' in item:
                                original, alias = item.split(' as ')
                                self.import_map[file_path][alias.strip()] = f"{original.strip()}@{resolved_path}"
                            else:
                                self.import_map[file_path][item] = f"{item}@{resolved_path}"

                    elif len(match.groups()) == 2:
                        # 处理默认导入
                        import_name, import_path = match.groups()
                        resolved_path = self._resolve_import_path(file_path, import_path)

                        if file_path not in self.import_map:
                            self.import_map[file_path] = {}
                        self.import_map[file_path][import_name] = f"{import_name}@{resolved_path}"

                    break

    def get_function_call_chain(self, function_name: str, file_path: str = None) -> List[str]:
        """获取函数的调用链"""
        target_nodes = []

        # 查找目标函数节点
        if file_path:
            node_id = f"{file_path}:{function_name}"
            if self.call_graph.has_node(node_id):
                target_nodes.append(node_id)
        else:
            # 查找所有同名函数
            for node_id in self.call_graph.nodes():
                if node_id.endswith(f":{function_name}"):
                    target_nodes.append(node_id)

        if not target_nodes:
            return []

        # 对每个目标节点，查找调用链
        all_chains = []
        for target_node in target_nodes:
            chains = self._find_call_chains_to_node(target_node)
            all_chains.extend(chains)

        return all_chains

    def _find_call_chains_to_node(self, target_node: str, max_depth: int = 10) -> List[str]:
        """查找到指定节点的所有调用链"""
        chains = []

        def dfs(current_node: str, path: List[str], depth: int):
            if depth > max_depth:
                return

            path.append(current_node)

            # 查找调用当前节点的所有节点
            predecessors = list(self.call_graph.predecessors(current_node))

            if not predecessors:
                # 这是调用链的起点
                chains.append(" -> ".join(reversed(path)))
            else:
                for pred in predecessors:
                    dfs(pred, path.copy(), depth + 1)

        dfs(target_node, [], 0)
        return chains

    def save_graph(self, output_path: str):
        """保存调用图到文件"""
        try:
            # 保存为JSON格式
            graph_data = {
                'nodes': [],
                'edges': []
            }

            for node_id, node_data in self.call_graph.nodes(data=True):
                graph_data['nodes'].append({
                    'id': node_id,
                    **node_data
                })

            for source, target, edge_data in self.call_graph.edges(data=True):
                graph_data['edges'].append({
                    'source': source,
                    'target': target,
                    **edge_data
                })

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(graph_data, f, indent=2, ensure_ascii=False)

            logger.info(f"调用图已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存调用图失败: {e}")

    @classmethod
    def load_graph(cls, input_path: str) -> nx.DiGraph:
        """从文件加载调用图"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                graph_data = json.load(f)

            graph = nx.DiGraph()

            for node in graph_data['nodes']:
                node_id = node.pop('id')
                graph.add_node(node_id, **node)

            for edge in graph_data['edges']:
                source = edge.pop('source')
                target = edge.pop('target')
                graph.add_edge(source, target, **edge)

            logger.info(f"调用图已从文件加载: {input_path}")
            return graph
        except Exception as e:
            logger.error(f"加载调用图失败: {e}")
            return nx.DiGraph()


def build_function_call_graph(repo_directory: str, cache_path: str = None) -> nx.DiGraph:
    """构建函数调用图的便捷函数"""
    builder = FunctionCallGraphBuilder(repo_directory)
    graph = builder.build_graph()

    if cache_path:
        builder.save_graph(cache_path)

    return graph


def get_function_call_chain(repo_directory: str, function_name: str,
                          file_path: str = None, cache_path: str = None) -> List[str]:
    """获取函数调用链的便捷函数"""
    if cache_path and os.path.exists(cache_path):
        # 从缓存加载
        graph = FunctionCallGraphBuilder.load_graph(cache_path)
        builder = FunctionCallGraphBuilder(repo_directory)
        builder.call_graph = graph
    else:
        # 重新构建
        builder = FunctionCallGraphBuilder(repo_directory)
        graph = builder.build_graph()
        if cache_path:
            builder.save_graph(cache_path)

    return builder.get_function_call_chain(function_name, file_path)
