"""
CodeMind API - 代码库索引模块

此模块提供代码库索引功能，将代码分割成块并生成嵌入。
"""

import os
import time
import json
from typing import Dict, Any, Optional, List
from tqdm import tqdm

from config.client import SweepConfig
from config.server import get_cache_directory
from core.lexical_search import prepare_lexical_search_index, SNIPPET_FORMAT
from core.vector_db import embed_text_array
from utils.function_call_graph import build_function_call_graph

from .common import logger, DEBUG_CODEMIND, process_extension_sources
from utils.file_metadata import detect_file_changes


def _perform_indexing(
    target_directory: str,
    cache_type: str,
    force_reindex: bool = False,
    llm_summary: bool = False,
    enable_contextual_retrieval: bool = False,
    seed: Optional[str] = None,
    debug_dir_override: Optional[str] = None,
    additional_stats: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    通用索引构建函数

    参数:
        target_directory: 要索引的目录路径
        cache_type: 缓存类型 ("main" 或 "extension")
        force_reindex: 是否强制重新索引
        llm_summary: 是否使用LLM生成代码摘要
        seed: 种子值，用于区分不同的缓存
        debug_dir_override: 调试目录覆盖路径
        additional_stats: 额外的统计信息

    返回:
        包含索引状态和统计信息的字典

    注意: 默认启用增量更新，除非设置 force_reindex=True
    """
    indexing_start_time = time.time()

    # 检查目录是否存在
    if not os.path.exists(target_directory):
        raise FileNotFoundError(f"目录不存在: {target_directory}")

    # 增量更新检测（默认启用，除非强制重新索引）
    file_changes = None
    if not force_reindex:
        logger.info("启用增量更新，检测文件变化...")
        file_changes = detect_file_changes(target_directory)

        if not file_changes.has_changes:
            logger.info("未检测到文件变化，跳过索引重建")

            # 检查是否存在缓存，如果存在则直接返回
            from core.lexical_search import get_lexical_cache_key
            from utils.cache_manager import get_snippets_cache
            from config.cache_constants import CacheType, EXTENSION_SEED

            cache_type_enum = CacheType.EXTENSION if seed == EXTENSION_SEED else CacheType.MAIN
            current_snippets_cache = get_snippets_cache(cache_type_enum.value)
            lexical_cache_key = get_lexical_cache_key(target_directory, seed=seed or "")
            snippets_results = current_snippets_cache.get(lexical_cache_key)

            if snippets_results:
                snippets, file_list = snippets_results
                unique_files = set(snippet.file_path for snippet in snippets)

                return {
                    "success": True,
                    "message": "未检测到文件变化，使用现有索引",
                    "stats": {
                        "snippet_count": len(snippets),
                        "file_count": len(unique_files),
                        "indexing_time": 0,
                        "embedding_time": 0,
                        "call_graph_time": 0,
                        "incremental": True,
                        "files_changed": 0,
                        "files_added": 0,
                        "files_deleted": 0,
                        "files_unchanged": len(file_changes.unchanged)
                    }
                }
            else:
                logger.info("未找到现有缓存，将进行全量索引")
        else:
            logger.info(f"检测到文件变化: 新增{len(file_changes.added)}, 修改{len(file_changes.modified)}, 删除{len(file_changes.deleted)}")

    # 注意：移除了内存缓存检查，现在依赖文件系统缓存

    # 创建配置
    sweep_config = SweepConfig()

    try:
        # 准备索引参数

        # 构建索引
        snippets, _ = prepare_lexical_search_index(
            repo_directory=target_directory,
            sweep_config=sweep_config,
            do_not_use_file_cache=False,
            seed=seed or "",
            llm_summary=llm_summary,
            enable_contextual_retrieval=enable_contextual_retrieval,
            force_reindex=force_reindex,
            file_changes=file_changes
        )

        logger.info(f"索引构建完成，共处理 {len(snippets)} 个代码片段")

        # 构建函数调用图
        logger.info("开始构建函数调用图...")
        call_graph_start_time = time.time()

        # 确定调用图缓存路径
        call_graph_cache_dir = os.path.join(get_cache_directory(), f"{cache_type}_call_graph")
        os.makedirs(call_graph_cache_dir, exist_ok=True)

        # 生成缓存文件名（基于目录路径的hash）
        import hashlib
        dir_hash = hashlib.md5(target_directory.encode()).hexdigest()
        call_graph_cache_path = os.path.join(call_graph_cache_dir, f"call_graph_{dir_hash}.json")

        try:
            # 构建函数调用图
            call_graph = build_function_call_graph(target_directory, call_graph_cache_path)
            call_graph_time = max(0, time.time() - call_graph_start_time)
            logger.info(f"函数调用图构建完成，耗时 {call_graph_time:.2f} 秒，包含 {len(call_graph.nodes)} 个节点")
        except Exception as e:
            logger.warning(f"函数调用图构建失败: {e}")
            call_graph_time = 0

        # 提前触发嵌入生成
        logger.info(f"开始生成代码片段嵌入...")
        embedding_start_time = time.time()

        # 准备代码片段内容
        snippet_contents = []
        desc_prefix = "扩展源" if cache_type == "extension" else ""
        for snippet in tqdm(snippets, desc=f"准备{desc_prefix}代码片段内容"):
            content = ""
            if snippet.summary_zh:
                content += f"//{snippet.summary_zh}\n"
            if snippet.summary_en:
                content += f"//{snippet.summary_en}\n"
            # 添加上下文信息到索引内容中
            if hasattr(snippet, 'contextual_info') and snippet.contextual_info:
                content += f"//{snippet.contextual_info}\n"
            content += snippet.get_snippet(add_ellipsis=False, add_lines=False)

            formatted_content = SNIPPET_FORMAT.format(
                file_path=snippet.file_path,
                contents=content
            )
            snippet_contents.append(formatted_content)

        # 如果开启了调试模式，保存调试信息
        if True:
            debug_dir = debug_dir_override or os.path.join(get_cache_directory(), "debug")
            os.makedirs(debug_dir, exist_ok=True)

            # 生成时间戳作为文件名的一部分
            timestamp = int(time.time())

            # 整合所有snippet信息到一个JSON文件
            debug_snippets = []
            for i, snippet in enumerate(snippets):
                # 获取对应的格式化内容
                snippet_content = snippet_contents[i] if i < len(snippet_contents) else ""

                debug_snippets.append({
                    "file_path": snippet.file_path,
                    "start_line": snippet.start,
                    "end_line": snippet.end,
                    "metadata": snippet.metadata,
                    "content": snippet_content,
                    "summary_zh": snippet.summary_zh,
                    "summary_en": snippet.summary_en,
                    "contextual_info": getattr(snippet, 'contextual_info', "")
                })

            debug_file = os.path.join(debug_dir, f"snippets_debug_{timestamp}.json")
            json_text = json.dumps(debug_snippets, ensure_ascii=False, indent=2)
            json_text = json_text.replace('\\n', '\n')

            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(json_text)

            logger.info(f"已将完整的代码片段调试信息保存到 {debug_file}")

        # 批量生成嵌入
        logger.info(f"为 {len(snippet_contents)} 个{desc_prefix}代码片段生成嵌入...")
        _ = embed_text_array(snippet_contents, cache_type=cache_type)

        # 计算嵌入时间
        embedding_time = max(0, time.time() - embedding_start_time)
        logger.info(f"{desc_prefix}嵌入生成完成，耗时 {embedding_time:.2f} 秒")

        # 计算总索引时间
        indexing_time = max(0, time.time() - indexing_start_time)

        # 计算统计信息
        unique_files = set(snippet.file_path for snippet in snippets)
        stats = {
            "snippet_count": len(snippets),
            "file_count": len(unique_files),
            "indexing_time": indexing_time,
            "embedding_time": embedding_time,
            "call_graph_time": call_graph_time,
            "incremental": True  # 默认启用增量更新
        }

        # 添加增量更新统计信息
        if file_changes:
            stats.update({
                "files_added": len(file_changes.added),
                "files_modified": len(file_changes.modified),
                "files_deleted": len(file_changes.deleted),
                "files_unchanged": len(file_changes.unchanged),
                "files_changed": len(file_changes.changed_files)
            })

        # 如果生成了摘要，添加摘要统计信息
        if llm_summary:
            summary_count = sum(1 for snippet in snippets if snippet.summary_zh or snippet.summary_en)
            stats["summary_count"] = summary_count

        # 如果启用了上下文检索，添加上下文统计信息
        if enable_contextual_retrieval:
            contextual_count = sum(1 for snippet in snippets if hasattr(snippet, 'contextual_info') and snippet.contextual_info)
            stats["contextual_count"] = contextual_count

        # 添加额外的统计信息
        if additional_stats:
            stats.update(additional_stats)

        # 注意：移除了内存缓存，现在依赖文件系统缓存

        return {
            "success": True,
            "message": f"索引创建成功，并已生成所有{desc_prefix}代码片段的嵌入",
            "stats": stats
        }

    except Exception as e:
        logger.exception(f"索引创建失败: {str(e)}")
        raise RuntimeError(f"索引创建失败: {str(e)}")

def index_codebase(repo_directory: str, force_reindex: bool = False, extension_sources: Optional[str] = None, llm_summary: bool = False, enable_contextual_retrieval: bool = False) -> Dict[str, Any]:
    """
    触发代码库索引生成

    此函数会分析指定目录中的代码，将其分割成块并生成嵌入。
    索引结果会被缓存，除非指定force_reindex=True。
    注意：repo_directory 不再使用 LLM 生成摘要，extension_sources 将单独处理。

    参数:
        repo_directory: 代码库目录路径
        force_reindex: 是否强制重新索引
        extension_sources: 扩展源JSON字符串（可选），包含本地文件夹路径或远程Git仓库地址的数组
        llm_summary: 是否使用LLM生成代码摘要（可选，仅对repo_directory有效）
        enable_contextual_retrieval: 是否启用上下文检索（可选）

    返回:
        包含索引状态和统计信息的字典
    """
    # 如果提供了extension_sources，处理扩展源（不再使用线程）
    if extension_sources:
        try:
            # 解析JSON字符串
            extension_sources_list = json.loads(extension_sources)
            if not isinstance(extension_sources_list, list):
                logger.error("extension_sources必须是JSON数组格式，跳过扩展源处理")
            else:
                # 主工程index完，再去index extension
                logger.info(f"将在主仓库索引完成后处理扩展源，包含 {len(extension_sources_list)} 个源")
        except json.JSONDecodeError as e:
            logger.error(f"解析extension_sources JSON失败: {str(e)}，跳过扩展源处理")
            extension_sources = None
        except Exception as e:
            logger.error(f"处理扩展源失败: {str(e)}")
            extension_sources = None

    # 构建主仓库索引
    # 调用通用索引函数，repo_directory 不使用 LLM 摘要
    result = _perform_indexing(
        target_directory=repo_directory,
        cache_type="main",
        force_reindex=force_reindex,
        llm_summary=False,  # repo_directory 不使用 LLM 摘要
        enable_contextual_retrieval=enable_contextual_retrieval
    )

    # 主仓库索引完成后，处理扩展源
    if extension_sources:
        try:
            extension_sources_list = json.loads(extension_sources)
            if isinstance(extension_sources_list, list):
                logger.info("主仓库索引完成，开始处理扩展源...")
                index_extension_sources(extension_sources_list, repo_directory, force_reindex, llm_summary, enable_contextual_retrieval)
        except Exception as e:
            logger.error(f"扩展源处理失败: {str(e)}")

    return result


def index_extension_sources(extension_sources_list: List[str], repo_directory: str, force_reindex: bool = False, llm_summary: bool = False, enable_contextual_retrieval: bool = False) -> None:
    """
    处理扩展源索引构建

    此函数复用 _perform_indexing 的实现，构建扩展源的索引和向量化，包含 LLM 摘要生成。
    索引结果存储在项目特定的缓存目录/extension_dir 目录中。

    参数:
        extension_sources_list: 扩展源列表，包含本地文件夹路径或远程Git仓库地址
        repo_directory: 主仓库目录路径
        force_reindex: 是否强制重新索引
        llm_summary: 是否使用LLM生成代码摘要
    """
    try:
        logger.info(f"开始处理扩展源索引，输入参数: extension_sources_list={extension_sources_list}, repo_directory={repo_directory}, force_reindex={force_reindex}")

        # 处理扩展源，创建软链接
        logger.info("开始处理扩展源，创建软链接...")
        processed_paths = process_extension_sources(extension_sources_list, repo_directory)
        logger.info(f"扩展源处理完成，成功处理的路径: {processed_paths}")

        if not processed_paths:
            logger.warning("没有成功处理任何扩展源，索引构建终止")
            return

        # 扩展源目录路径
        extension_dir_path = os.path.join(get_cache_directory(), "extension_dir")
        logger.info(f"扩展源目录路径: {extension_dir_path}")

        # 扩展源专用缓存目录
        extension_cache_dir = os.path.join(get_cache_directory(), "extension_cache")
        os.makedirs(extension_cache_dir, exist_ok=True)
        logger.info(f"扩展源缓存目录: {extension_cache_dir}")

        # 检查扩展源目录是否存在且包含文件
        if not os.path.exists(extension_dir_path):
            logger.error(f"扩展源目录不存在: {extension_dir_path}")
            return

        # 列出扩展源目录内容
        try:
            dir_contents = os.listdir(extension_dir_path)
            logger.info(f"扩展源目录内容: {dir_contents}")
            if not dir_contents:
                logger.warning("扩展源目录为空")
                return
        except Exception as e:
            logger.error(f"无法读取扩展源目录: {str(e)}")
            return

        # 使用统一的常量
        from config.cache_constants import EXTENSION_SEED

        # 准备额外的统计信息
        additional_stats = {
            "extension_sources_count": len(processed_paths)
        }

        # 调用通用索引函数
        result = _perform_indexing(
            target_directory=extension_dir_path,
            cache_type="extension",
            force_reindex=force_reindex,
            llm_summary=llm_summary,
            enable_contextual_retrieval=enable_contextual_retrieval,
            seed=EXTENSION_SEED,
            debug_dir_override=os.path.join(extension_cache_dir, "debug"),
            additional_stats=additional_stats
        )

        logger.info(f"扩展源索引构建完成: {result['stats']}")

    except Exception as e:
        # 特殊处理解释器关闭的情况
        if "cannot schedule new futures after interpreter shutdown" in str(e):
            logger.warning("解释器正在关闭，扩展源索引构建被中断")
            return

        logger.exception(f"扩展源索引构建失败: {str(e)}")
        # 参考 index_codebase 的错误处理方式
        raise RuntimeError(f"扩展源索引构建失败: {str(e)}")