"""
CodeMind API - 扩展源搜索模块

此模块提供扩展源代码库语义搜索功能，返回与查询最相关的代码片段。
扩展源的索引和向量化在后台异步构建，包含 LLM 摘要。
"""

import os
import time
from typing import Dict, Any

from utils.github_utils import MockClonedRepo

from .common import logger


def extension_search(repo_directory: str, query: str, limit: int = 5, use_rerank: bool = False, min_score: float = 0.5) -> Dict[str, Any]:
    """
    搜索扩展源代码库

    使用扩展源的索引搜索代码库，返回最相关的代码片段。
    扩展源的索引包含 LLM 生成的摘要信息。

    参数:
        repo_directory: 代码库目录路径，用于定位对应的扩展源索引
        query: 搜索查询
        limit: 返回结果数量限制，默认为5
        use_rerank: 是否使用rerank步骤，默认为False（不启用rerank）
        min_score: 最低相关度阈值，默认为0.5，只返回相关度大于等于此值的结果

    返回:
        相关代码片段列表，JSON格式包含code、msg、cost_time和data字段
    """
    start_time = time.time()

    try:
        # 检查目录是否存在
        if not os.path.exists(repo_directory):
            elapsed_time = max(0, time.time() - start_time)
            return {
                "code": 1,
                "msg": f"目录不存在: {repo_directory}",
                "cost_time": elapsed_time,
                "data": []
            }

        # 构建扩展源目录路径 - 基于 repo_directory 定位对应的扩展源
        from config.server import get_cache_directory
        extension_dir_path = os.path.join(get_cache_directory(), "extension_dir")

        # 检查扩展源目录是否存在
        if not os.path.exists(extension_dir_path):
            elapsed_time = max(0, time.time() - start_time)
            return {
                "code": 1,
                "msg": f"扩展源目录不存在: {extension_dir_path}",
                "cost_time": elapsed_time,
                "data": []
            }

        # 检查扩展源磁盘缓存是否存在 - 不依赖内存缓存
        from core.lexical_search import get_lexical_cache_key
        from config.cache_constants import EXTENSION_SEED, CacheType
        from utils.cache_path_utils import get_lexical_index_cache_path
        from utils.cache_manager import get_snippets_cache

        # 生成扩展源的缓存键
        lexical_cache_key = get_lexical_cache_key(extension_dir_path, seed=EXTENSION_SEED)

        # 检查词法索引缓存是否存在
        index_cache_path = get_lexical_index_cache_path(lexical_cache_key, CacheType.EXTENSION)

        # 检查snippets缓存是否存在
        extension_snippets_cache = get_snippets_cache(CacheType.EXTENSION.value)

        if not os.path.exists(index_cache_path) or lexical_cache_key not in extension_snippets_cache:
            elapsed_time = max(0, time.time() - start_time)
            return {
                "code": 1,
                "msg": f"扩展源索引未找到，请先运行 index_codebase 并提供 extension_sources 参数",
                "cost_time": elapsed_time,
                "data": []
            }

        # 创建MockClonedRepo对象
        cloned_repo = MockClonedRepo(
            _repo_dir=extension_dir_path,
            repo_full_name=f"extension_sources_{os.path.basename(extension_dir_path)}"
        )

        # 直接调用prep_snippets函数
        # 重要：传递 seed="extension" 以使用扩展源的独立缓存
        from utils.ticket_utils import prep_snippets
        from config.cache_constants import EXTENSION_SEED

        snippets = prep_snippets(
            cloned_repo=cloned_repo,
            query=query,
            use_multi_query=False,
            NUM_SNIPPETS_TO_KEEP=0,
            skip_analyze_agent=True,
            skip_reranking=not use_rerank,
            k=limit,
            seed=EXTENSION_SEED  # 关键：使用扩展源缓存
        )

        # 转换为API响应格式并筛选相关度达到阈值的结果
        results = []
        for snippet in snippets:
            passage = ""
            if snippet.metadata and "imports" in snippet.metadata and snippet.metadata["imports"]:
                passage = "\n".join(snippet.metadata["imports"])

            # 添加摘要信息到passage（扩展源特有的功能）
            summary_parts = []
            if hasattr(snippet, 'summary_zh') and snippet.summary_zh:
                summary_parts.append(f"中文摘要: {snippet.summary_zh}")
            if hasattr(snippet, 'summary_en') and snippet.summary_en:
                summary_parts.append(f"英文摘要: {snippet.summary_en}")

            if summary_parts:
                passage = "\n".join(summary_parts) + "\n" + passage
            passage += snippet.get_snippet(add_ellipsis=False, add_lines=False)

            score = float(snippet.score)
            if score >= min_score:
                results.append({
                    "query": query,
                    "passage": passage,
                    "file_path": snippet.original_path if snippet.original_path else snippet.file_path,
                    "start": snippet.start,
                    "end": snippet.end,
                    "score": score,
                    "source_type": "extension"  # 标识这是扩展源的结果
                })

        results = sorted(results, key=lambda x: x["score"], reverse=True)[:limit]

        elapsed_time = max(0, time.time() - start_time)

        response = {
            "code": 0,
            "msg": "success",
            "cost_time": elapsed_time,
            "data": results
        }

        logger.info(f"搜索完成，耗时 {elapsed_time:.2f} 秒，找到 {len(results)} 个结果")
        return response

    except Exception as e:
        logger.exception(f"搜索失败: {str(e)}")
        elapsed_time = max(0, time.time() - start_time) if 'start_time' in locals() else 0
        error_response = {
            "code": 1,
            "msg": f"搜索失败: {str(e)}",
            "cost_time": elapsed_time,
            "data": []
        }
        return error_response