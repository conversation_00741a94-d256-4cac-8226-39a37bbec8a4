"""
CodeMind API - 知识库搜索模块

此模块提供基于外部知识库的搜索功能，返回与查询最相关的文档。
"""

import time
import json
import requests
from typing import Dict, Any

from .common import logger

def knowledge_search(query: str, limit: int = 3, timeout: int = 30) -> Dict[str, Any]:
    """
    基于知识库的搜索

    调用外部API进行知识库搜索，返回最相关的结果。

    参数:
        query: 搜索查询
        limit: 返回结果数量限制，默认为3
        timeout: 请求超时时间（秒），默认为30秒

    返回:
        包含搜索结果的字典
    """
    # 检查查询是否为空
    if not query or not query.strip():
        return {
            "code": 1,
            "msg": "查询不能为空",
            "cost_time": 0,
            "data": []
        }

    start_time = time.time()
    
    try:
        # API调用信息
        url = "https://aiplat-gateway.devops.beta.xiaohongshu.com/allin-workflow-codemindbridges/pipelines/main"
        headers = {
            'content-type': 'application/json',
            'APP_ID': 'codemindbridges',
            'APP_KEY': 'o8PzmCjD8E/N6L6BGzSq8ODQRZ/OD7aOECR1xX6SPZw='
        }
        data = {
            "query": query
        }

        # 发起API请求
        logger.info(f"向知识库发送查询: {query}")
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        
        # 检查响应状态
        if response.status_code != 200:
            error_msg = f"API请求失败: HTTP {response.status_code}"
            logger.error(error_msg)
            elapsed_time = max(0, time.time() - start_time)  # 确保非负值
            return {
                "code": 1,
                "msg": error_msg,
                "cost_time": elapsed_time,
                "data": []
            }

        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError:
            error_msg = "无法解析API响应的JSON数据"
            logger.error(error_msg)
            elapsed_time = max(0, time.time() - start_time)  # 确保非负值
            return {
                "code": 1,
                "msg": error_msg,
                "cost_time": elapsed_time,
                "data": []
            }

        # 直接返回原始响应数据
        # 如果需要限制结果数量，仅返回指定数量的replies
        if "replies" in result and limit > 0 and len(result["replies"]) > limit:
            result["replies"] = result["replies"][:limit]

        # 计算耗时，确保非负值
        elapsed_time = max(0, time.time() - start_time)
        
        # 构建成功响应
        success_response = {
            "code": 0,
            "msg": "success",
            "cost_time": elapsed_time,
            "data": result  # 直接返回完整结果
        }

        logger.info(f"知识库搜索完成，耗时 {elapsed_time:.2f} 秒")
        return success_response
    except Exception as e:
        logger.exception(f"知识库搜索失败: {str(e)}")
        elapsed_time = max(0, time.time() - start_time)  # 确保非负值
        error_response = {
            "code": 1,
            "msg": f"搜索失败: {str(e)}",
            "cost_time": elapsed_time,
            "data": []
        }
        return error_response 