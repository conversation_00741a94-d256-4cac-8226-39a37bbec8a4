"""
CodeMind API - Grep搜索模块

此模块提供基于ripgrep的文本搜索功能，用于在代码库中搜索关键词或正则表达式。
"""

import os
import time
import re
import subprocess
import platform
from typing import Dict, Any, List

from .common import logger
from config.server import get_cache_directory

def grep_search(repo_directory: str, query: str, context_lines: int = 5, limit: int = 5) -> Dict[str, Any]:
    """
    使用ripgrep在代码库中搜索关键词

    参数:
        repo_directory: 代码库目录路径
        query: 要搜索的关键词或正则表达式
        context_lines: 匹配行前后显示的上下文行数，默认为5
        limit: 返回结果数量限制，默认为5

    返回:
        包含搜索结果的字典
    """
    start_time = time.time()

    # 检查目录是否存在
    if not os.path.exists(repo_directory):
        raise FileNotFoundError(f"目录不存在: {repo_directory}")

    # 添加extension_dir目录
    extension_dir_path = os.path.join(get_cache_directory(), "extension_dir")
    
    # 检查ripgrep是否已安装
    rg_installed = True
    try:
        check = subprocess.run(
            ["rg", "--version"],
            capture_output=True,
            text=True
        )
        if check.returncode != 0:
            rg_installed = False
    except FileNotFoundError:
        rg_installed = False

    # 如果未安装，尝试自动安装（仅限Mac系统）
    if not rg_installed:
        is_mac = platform.system() == 'Darwin'
        if is_mac:
            logger.info("检测到ripgrep未安装，尝试自动安装...")
            try:
                logger.info("执行: brew install ripgrep")
                install_result = subprocess.run(
                    ["brew", "install", "ripgrep"],
                    capture_output=True,
                    text=True
                )
                if install_result.returncode == 0:
                    logger.info("ripgrep安装成功")
                    rg_installed = True
                else:
                    logger.error(f"ripgrep安装失败: {install_result.stderr}")
                    return {
                        "code": 1,
                        "msg": f"ripgrep未安装，自动安装失败: {install_result.stderr}。请手动执行'brew install ripgrep'",
                        "cost_time": 0,
                        "data": []
                    }
            except Exception as e:
                logger.error(f"尝试安装ripgrep时出错: {str(e)}")
                return {
                    "code": 1,
                    "msg": f"ripgrep未安装，自动安装时出错: {str(e)}。请手动执行'brew install ripgrep'",
                    "cost_time": 0,
                    "data": []
                }
        else:
            # 非Mac系统提供安装指导
            install_guide = ""
            if platform.system() == 'Linux':
                install_guide = "在Ubuntu/Debian上: sudo apt-get install ripgrep\n在CentOS/RHEL上: sudo yum install ripgrep"
            elif platform.system() == 'Windows':
                install_guide = "使用Chocolatey: choco install ripgrep\n或使用Scoop: scoop install ripgrep"

            return {
                "code": 1,
                "msg": f"ripgrep未安装。{install_guide}",
                "cost_time": 0,
                "data": []
            }

    try:
        # 创建一个函数来处理ripgrep的结果
        def process_ripgrep_results(directory: str) -> List[Dict]:
            if not os.path.exists(directory):
                logger.info(f"目录不存在，跳过搜索: {directory}")
                return []
                
            # 执行ripgrep命令
            response = subprocess.run(
                " ".join([
                    "rg",
                    "-n",  # 显示行号
                    "-i",  # 忽略大小写
                    "--follow",  # 跟随软链接
                    "-g", "!**/node_modules/**",  # 排除任何位置的node_modules目录
                    "-g", "!**/.venv/**",  # 排除任何位置的.venv目录
                    "-g", "!**/venv/**",  # 排除任何位置的venv目录
                    "-g", "!**/build/**",  # 排除任何位置的build目录
                    "-g", "!**/patch/**",  # 排除任何位置的patch目录
                    "-g", "!**/dist/**",  # 排除任何位置的dist目录
                    f"-C={context_lines}",  # 上下文行数
                    "--heading",  # 用文件名作为标题
                    "--sort-files",  # 按文件名排序
                    query,
                    directory
                ]),
                shell=True,
                capture_output=True,
                text=True,
                cwd=directory,
            )
            
            if response.returncode != 0 and not response.stdout:
                return []
                
            # 过滤输出（限制每行长度）
            max_line_length = 300
            filtered_output = ""
            for line in response.stdout.splitlines():
                if len(line) < max_line_length:
                    filtered_output += line + "\n"
                else:
                    filtered_output += line[:max_line_length] + f"... (省略 {len(line) - max_line_length} 个字符)\n"

            # 按文件名拆分结果
            file_results = []

            # 使用双换行符分割不同文件的结果块
            blocks = filtered_output.strip().split('\n\n')

            for block in blocks:
                if not block.strip():
                    continue

                # 分割每个块的行
                lines = block.strip().split('\n')

                # 第一行是文件名
                if not lines:
                    continue

                file_path = lines[0].strip()

                # 其余行是内容
                content_lines = []
                for line in lines[1:]:
                    # 跳过分隔符行
                    if line.strip() == '--':
                        continue
                    content_lines.append(line)

                # 只有当有内容时才添加结果
                if content_lines:
                    file_results.append({
                        "file_path": file_path,
                        "content": '\n'.join(content_lines)
                    })
                    
            # 计算每个文件的匹配次数和行号范围
            for result in file_results:
                # 计算匹配行数（包含行号的行）
                match_count = 0
                line_numbers = []

                for line in result["content"].split('\n'):
                    # 匹配行通常包含行号和冒号，如 "10:    import os"
                    line_match = re.match(r'^(\d+):', line.strip())
                    if line_match:
                        match_count += 1
                        line_numbers.append(int(line_match.group(1)))

                result["match_count"] = match_count

                # 添加行号范围
                if line_numbers:
                    min_line = min(line_numbers)
                    max_line = max(line_numbers)
                    result["line_range"] = [min_line, max_line]
                else:
                    result["line_range"] = [0, 0]
                    
            return file_results

        # 搜索主目录
        main_results = process_ripgrep_results(repo_directory)
        logger.info(f"主目录搜索完成: {repo_directory}, 找到 {len(main_results)} 个结果")
        
        # 搜索扩展目录
        extension_results = []
        if os.path.exists(extension_dir_path):
            extension_results = process_ripgrep_results(extension_dir_path)
            logger.info(f"扩展目录搜索完成: {extension_dir_path}, 找到 {len(extension_results)} 个结果")
        
        # 合并结果
        all_results = main_results + extension_results
        
        # 按匹配次数降序排序
        all_results.sort(key=lambda x: x["match_count"], reverse=True)

        # 限制结果数量
        all_results = all_results[:limit]

        # 计算总匹配行数
        total_match_count = sum(result["match_count"] for result in all_results)

        # 转换为与codebase_search相似的格式
        formatted_results = []
        for result in all_results:
            line_range = result.get("line_range", [0, 0])

            # 处理内容，移除行号前缀
            content_lines = []
            for line in result["content"].split('\n'):
                # 匹配行号前缀，如 "10:    import os"
                line_match = re.match(r'^\d+:(.*)', line)
                if line_match:
                    # 提取冒号后的内容
                    content_lines.append(line_match.group(1))
                else:
                    # 没有行号前缀的行直接添加
                    content_lines.append(line)

            # 合并处理后的内容
            clean_content = '\n'.join(content_lines)

            formatted_results.append({
                "query": query,
                "passage": clean_content,
                "file_path": result["file_path"],
                "start": line_range[0],
                "end": line_range[1],
                "score": result["match_count"]  # 使用匹配次数作为分数
            })

        # 计算耗时，确保非负值
        elapsed_time = max(0, time.time() - start_time)

        # 保留原始数据以便向后兼容
        results = {
            "code": 0,
            "msg": "success",
            "cost_time": elapsed_time,
            "data": formatted_results,
            "_original_data": {
                "query": query,
                "match_count": total_match_count,
                "file_results": all_results
            }
        }

        logger.info(f"Grep搜索完成，耗时 {elapsed_time:.2f} 秒")
        return results
    except Exception as e:
        logger.exception(f"Grep搜索失败: {str(e)}")
        # 计算耗时，确保非负值（如果start_time已定义）
        elapsed_time = max(0, time.time() - start_time) if 'start_time' in locals() else 0
        error_response = {
            "code": 1,
            "msg": f"搜索失败: {str(e)}",
            "cost_time": elapsed_time,
            "data": []
        }
        return error_response 