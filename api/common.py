"""
CodeMind API - 共享变量和工具

此模块包含API间共享的变量、工具和配置。
"""

import os
import logging
import subprocess
import shutil
from typing import List, Dict, Any
from urllib.parse import urlparse
from config.server import get_cache_directory, REPO_DIRECTORY

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 调试配置
DEBUG_CODEMIND = os.environ.get("DEBUG_CODEMIND", "false").lower() == "true"

def is_git_url(url: str) -> bool:
    """
    判断是否为 Git 仓库地址

    参数:
        url: 要检查的 URL 字符串

    返回:
        bool: 如果是 Git 仓库地址返回 True，否则返回 False
    """
    if not url:
        return False

    # 检查是否以 http 或 https 开头
    if url.startswith('http://') or url.startswith('https://'):
        return True
    
    return False

def normalize_git_url(url: str) -> str:
    """
    标准化 Git URL，如果没有 .git 结尾则自动补充

    参数:
        url: Git 仓库 URL

    返回:
        str: 标准化后的 Git URL
    """
    if not url:
        return url
    
    # 如果是 http/https URL 且没有 .git 结尾，则补充
    if (url.startswith('http://') or url.startswith('https://')) and not url.endswith('.git'):
        return url + '.git'
    
    return url

def get_repo_name_from_url(git_url: str) -> str:
    """
    从 Git URL 中提取仓库名称

    参数:
        git_url: Git 仓库 URL

    返回:
        str: 仓库名称
    """
    if git_url.startswith('git@'):
        # 处理 SSH 格式: **************:user/repo.git
        parts = git_url.split(':')[-1]
        repo_name = parts.replace('.git', '').split('/')[-1]
    else:
        # 处理 HTTPS 格式: https://github.com/user/repo.git
        parsed = urlparse(git_url)
        repo_name = parsed.path.strip('/').replace('.git', '').split('/')[-1]

    return repo_name

def clone_or_update_repo(git_url: str, target_dir: str) -> bool:
    """
    克隆或更新 Git 仓库

    参数:
        git_url: Git 仓库 URL
        target_dir: 目标目录路径

    返回:
        bool: 操作成功返回 True，失败返回 False
    """
    try:
        if os.path.exists(target_dir):
            # 目录存在，尝试更新
            logger.info(f"更新现有仓库: {target_dir}")
            result = subprocess.run(
                ["git", "pull"],
                cwd=target_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            if result.returncode == 0:
                logger.info(f"仓库更新成功: {git_url}")
                return True
            else:
                logger.warning(f"仓库更新失败，尝试重新克隆: {result.stderr}")
                shutil.rmtree(target_dir)

        # 克隆仓库
        logger.info(f"克隆仓库: {git_url} -> {target_dir}")
        result = subprocess.run(
            ["git", "clone", git_url, target_dir],
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )

        if result.returncode == 0:
            logger.info(f"仓库克隆成功: {git_url}")
            return True
        else:
            logger.error(f"仓库克隆失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error(f"Git 操作超时: {git_url}")
        return False
    except Exception as e:
        logger.error(f"Git 操作异常: {str(e)}")
        return False

def process_extension_sources(extension_sources: List[str], repo_directory: str) -> List[str]:
    """
    处理扩展源列表，支持本地路径和远程 Git 仓库

    参数:
        extension_sources: 扩展源列表，包含本地路径或 Git 仓库 URL
        repo_directory: 主仓库目录路径

    返回:
        List[str]: 处理后的本地路径列表
    """
    processed_paths = []
    extension_dir_path = os.path.join(get_cache_directory(), "extension_dir")

    # 如果目录存在，先删除再创建
    if os.path.exists(extension_dir_path):
        shutil.rmtree(extension_dir_path)
    os.makedirs(extension_dir_path)
    logger.info(f"创建扩展源目录: {extension_dir_path}")

    for i, source in enumerate(extension_sources):
        logger.info(f"处理第 {i+1}/{len(extension_sources)} 个扩展源: {source}")

        if is_git_url(source):
            # 处理远程 Git 仓库，标准化 URL
            normalized_url = normalize_git_url(source)
            repo_name = get_repo_name_from_url(normalized_url)
            repo_target_dir = os.path.join(REPO_DIRECTORY, repo_name)

            if clone_or_update_repo(normalized_url, repo_target_dir):
                # 克隆/更新成功，创建软链接
                symlink_target = os.path.join(extension_dir_path, repo_name)
                try:
                    os.symlink(repo_target_dir, symlink_target, target_is_directory=True)
                    processed_paths.append(symlink_target)
                    logger.info(f"已创建远程仓库软链接: {repo_target_dir} -> {symlink_target}")
                except Exception as e:
                    logger.error(f"创建远程仓库软链接失败: {str(e)}")
            else:
                logger.error(f"无法处理远程仓库: {normalized_url}")
        else:
            # 处理本地路径
            if os.path.isabs(source):
                source_path = source
            else:
                source_path = os.path.join(repo_directory, source)

            if os.path.exists(source_path):
                symlink_target = os.path.join(extension_dir_path, os.path.basename(source_path))
                try:
                    os.symlink(source_path, symlink_target, target_is_directory=True)
                    processed_paths.append(symlink_target)
                    logger.info(f"已创建本地路径软链接: {source_path} -> {symlink_target}")
                except Exception as e:
                    logger.error(f"创建本地路径软链接失败: {str(e)}")
            else:
                logger.warning(f"指定的本地路径不存在: {source_path}")

    return processed_paths

# 定义响应类型
class SnippetResponse:
    """代码片段响应"""
    def __init__(self, file_path: str, start: int, end: int, content: str, score: float):
        self.file_path = file_path
        self.start = start
        self.end = end
        self.content = content
        self.score = score

    def to_dict(self):
        return {
            "file_path": self.file_path,
            "start": self.start,
            "end": self.end,
            "content": self.content,
            "score": self.score
        }