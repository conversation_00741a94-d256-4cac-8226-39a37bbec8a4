"""
CodeMind API - 代码摘要生成模块

此模块提供代码摘要生成功能，使用LLM为代码片段生成中英文摘要。
"""

import time
import concurrent.futures
from typing import List, Tuple
from tqdm import tqdm

from llm.llm_chat import chat_with_llm
from api.common import logger

def estimate_token_count(text: str) -> int:
    """
    估算文本的token数量（粗略估计，每4个字符约为1个token）
    
    参数:
        text: 输入文本
        
    返回:
        估计的token数量
    """
    return len(text) // 4 + 1

def generate_batch_summaries_in_one_call(snippets_batch: List[Tuple[str, str, int]], max_tokens: int = 8000) -> List[Tuple[str, str, int]]:
    """
    在一次LLM调用中为多个代码片段生成中英文摘要
    
    参数:
        snippets_batch: 包含(代码内容, 文件路径, 索引)的元组列表
        max_tokens: 单次请求的最大token数量
        
    返回:
        包含(中文摘要, 英文摘要, 索引)的元组列表
    """
    if not snippets_batch:
        return []
    
    logger.info(f"开始批量处理 {len(snippets_batch)} 个代码片段，索引范围: {[idx for _, _, idx in snippets_batch]}")
    
    # 系统提示，指导LLM生成代码摘要
    system_prompt = """你是一个代码分析专家，擅长分析和总结代码片段的功能和作用。
请分析提供的多个代码片段，为每个片段生成简洁的中英文摘要，帮助开发者快速理解代码的主要功能和用途。
摘要应该包含代码的核心功能、主要算法或设计模式，以及可能的用途。
请确保摘要简洁明了，不超过100个字符。
"""
    
    # 准备批量处理的代码片段
    snippets_text = ""
    current_tokens = estimate_token_count(system_prompt)
    processed_snippets = []
    
    for i, (content, file_path, idx) in enumerate(snippets_batch):
        snippet_text = f"""代码片段 #{i+1}
文件路径: {file_path}

```
{content}
```

"""
        snippet_tokens = estimate_token_count(snippet_text)
        
        # 如果添加这个片段会超过token限制，先处理当前批次
        if current_tokens + snippet_tokens > max_tokens and processed_snippets:
            # 处理当前批次
            logger.info(f"当前批次已达到token限制({current_tokens + snippet_tokens}/{max_tokens})，处理 {len(processed_snippets)} 个代码片段")
            results = process_snippets_batch(system_prompt, snippets_text, processed_snippets)
            
            # 重置为新批次
            snippets_text = snippet_text
            current_tokens = estimate_token_count(system_prompt) + snippet_tokens
            processed_snippets = [(content, file_path, idx)]
        else:
            # 添加到当前批次
            snippets_text += snippet_text
            current_tokens += snippet_tokens
            processed_snippets.append((content, file_path, idx))
    
    # 处理剩余的片段
    if processed_snippets:
        logger.info(f"处理最后一批 {len(processed_snippets)} 个代码片段")
        return process_snippets_batch(system_prompt, snippets_text, processed_snippets)
    
    return []

def parse_llm_response(response: str, processed_snippets: List[Tuple[str, str, int]]) -> List[Tuple[str, str, int]]:
    """
    解析LLM返回的摘要响应
    
    参数:
        response: LLM返回的响应文本
        processed_snippets: 处理的代码片段列表
        
    返回:
        包含(中文摘要, 英文摘要, 索引)的元组列表
    """
    results = []
    
    # 按"代码片段 #"分割响应
    chunks = response.split("代码片段 #")[1:]  # 跳过第一个空元素
    
    for i, chunk in enumerate(chunks):
        if i >= len(processed_snippets):
            break
            
        summary_zh = ""
        summary_en = ""
        
        # 在每个块中查找摘要
        for line in chunk.split('\n'):
            line = line.strip()
            if line.startswith("中文摘要："):
                summary_zh = line.replace("中文摘要：", "").strip()
            elif line.startswith("英文摘要："):
                summary_en = line.replace("英文摘要：", "").strip()
        
        # 获取原始索引
        original_idx = processed_snippets[i][2]
        results.append((summary_zh, summary_en, original_idx))
        logger.debug(f"解析片段 {i+1}，原始索引: {original_idx}, 中文摘要: '{summary_zh[:30]}...', 英文摘要: '{summary_en[:30]}...'")
    
    return results

def process_snippets_batch(system_prompt: str, snippets_text: str, processed_snippets: List[Tuple[str, str, int]]) -> List[Tuple[str, str, int]]:
    """
    处理一批代码片段并生成摘要
    
    参数:
        system_prompt: 系统提示
        snippets_text: 包含所有代码片段的文本
        processed_snippets: 处理的代码片段列表
        
    返回:
        包含(中文摘要, 英文摘要, 索引)的元组列表
    """
    # 用户提示，包含多个代码片段
    user_prompt = f"""请为以下多个代码片段生成中英文摘要：

{snippets_text}

请按照以下格式返回每个代码片段的摘要：
代码片段 #1:
中文摘要：[简洁的中文摘要，不超过100个字符]
英文摘要：[简洁的英文摘要，不超过100个字符]

代码片段 #2:
中文摘要：[简洁的中文摘要，不超过100个字符]
英文摘要：[简洁的英文摘要，不超过100个字符]

...以此类推
"""
    
    # 为所有输入片段初始化空摘要结果
    input_indices = [idx for _, _, idx in processed_snippets]
    logger.info(f"处理批次的输入索引: {input_indices}")
    results = [("", "", idx) for _, _, idx in processed_snippets]
    
    try:
        # 调用LLM生成摘要
        logger.info(f"调用LLM生成 {len(processed_snippets)} 个代码片段的摘要，预估token数: {estimate_token_count(user_prompt)}")
        start_time = time.time()
        response = chat_with_llm(system_prompt, user_prompt)
        logger.info(f"LLM调用完成，耗时 {time.time() - start_time:.2f} 秒")
        
        # 解析返回的摘要
        parsed_results = parse_llm_response(response, processed_snippets)
        
        # 将解析的结果更新到初始化的结果中
        for summary_zh, summary_en, original_idx in parsed_results:
            # 查找该索引在当前批次中的位置
            for i, (_, _, idx) in enumerate(processed_snippets):
                if idx == original_idx:
                    results[i] = (summary_zh, summary_en, original_idx)
                    break
        
        # 统计成功生成摘要的数量
        success_count = sum(1 for zh, en, _ in results if zh or en)
        logger.info(f"成功生成 {success_count}/{len(results)} 个代码片段的摘要")
        
        return results
    except Exception as e:
        logger.error(f"批量生成代码摘要失败: {str(e)}")
        # 返回初始化的空摘要，确保索引对应
        return results

def generate_batch_summaries(snippets_batch: List[Tuple[str, str, int]]) -> List[Tuple[str, str, int]]:
    """
    批量生成代码摘要
    
    参数:
        snippets_batch: 包含(代码内容, 文件路径, 索引)的元组列表
        
    返回:
        包含(中文摘要, 英文摘要, 索引)的元组列表
    """
    # 使用优化的批处理方法
    return generate_batch_summaries_in_one_call(snippets_batch)

def generate_code_summaries(snippets_data: List[Tuple[str, str, int]], max_workers: int = 4) -> List[Tuple[str, str, int]]:
    """
    为多个代码片段生成摘要，支持并行处理
    
    参数:
        snippets_data: 包含(代码内容, 文件路径, 索引)的元组列表
        max_workers: 最大并行工作线程数
        
    返回:
        包含(中文摘要, 英文摘要, 索引)的元组列表
    """
    if not snippets_data:
        return []
    
    input_indices = [idx for _, _, idx in snippets_data]
    logger.info(f"开始为 {len(snippets_data)} 个代码片段生成摘要，索引范围: {min(input_indices)}-{max(input_indices)}")
    
    # 计算代码片段的平均token数
    avg_tokens = sum(len(content) for content, _, _ in snippets_data) / (len(snippets_data) * 4)
    
    # 根据平均token数动态调整批次大小，确保每批次的总token数不超过模型限制
    # 假设模型最大token数为8000，系统提示和用户提示约占2000 tokens
    max_batch_tokens = 6000  # 留出2000 tokens作为提示和其他开销
    batch_size = max(1, min(20, int(max_batch_tokens / avg_tokens)))
    
    logger.info(f"代码片段平均估算token数: {avg_tokens:.1f}，动态批次大小: {batch_size}")
    batches = [snippets_data[i:i + batch_size] for i in range(0, len(snippets_data), batch_size)]
    
    # 使用线程池并行处理批次
    all_summaries = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_batch = {executor.submit(generate_batch_summaries, batch): batch for batch in batches}
        
        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(future_to_batch), total=len(batches), desc="生成代码摘要"):
            try:
                batch_results = future.result()
                all_summaries.extend(batch_results)
            except Exception as e:
                logger.error(f"处理批次时出错: {str(e)}")
    
    # 按索引排序摘要结果
    all_summaries.sort(key=lambda x: x[2])
    
    # 验证返回结果的完整性
    expected_indices = set(input_indices)
    actual_indices = set(idx for _, _, idx in all_summaries)
    
    if expected_indices != actual_indices:
        missing_indices = expected_indices - actual_indices
        extra_indices = actual_indices - expected_indices
        
        if missing_indices:
            logger.warning(f"缺失摘要的索引: {sorted(missing_indices)}")
            # 为缺失的索引添加空摘要
            for idx in missing_indices:
                all_summaries.append(("", "", idx))
        
        if extra_indices:
            logger.warning(f"多余摘要的索引: {sorted(extra_indices)}")
            # 移除多余的摘要
            all_summaries = [(zh, en, idx) for zh, en, idx in all_summaries if idx in expected_indices]
        
        # 重新排序
        all_summaries.sort(key=lambda x: x[2])
    
    success_count = sum(1 for zh, en, _ in all_summaries if zh or en)
    logger.info(f"摘要生成完成: {success_count}/{len(all_summaries)} 个代码片段成功生成摘要")
    
    return all_summaries
