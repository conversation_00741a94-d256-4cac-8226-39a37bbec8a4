import time
import concurrent.futures
import os
from typing import List
from tqdm import tqdm

from llm.llm_chat import chat_with_llm
from api.common import logger
from core.entities import Snippet
from utils.file_utils import read_file_with_fallback_encodings

def generate_contextual_info_for_snippet(
    snippet_content: str,
    file_path: str,
    surrounding_context: str = ""
) -> str:
    """
    为单个代码片段生成上下文信息

    参数:
        snippet_content: 代码片段内容
        file_path: 文件路径
        surrounding_context: 周围的上下文信息（可选）

    返回:
        生成的上下文信息
    """
    # 构建完整文档上下文
    whole_document = f"文件路径: {file_path}"
    if surrounding_context:
        whole_document += f"\n文件描述: {surrounding_context}"

    # 使用Anthropic官方的提示词格式，只添加中英文要求
    system_prompt = """You are a professional code analysis assistant, skilled at generating concise and useful contextual information for code snippets to improve search retrieval accuracy."""

    user_prompt = f"""<document>
{whole_document}
</document>

Here is the chunk we want to situate within the whole document
<chunk>
{snippet_content}
</chunk>

Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk. Provide the context in both Chinese and English, separated by " | ". Answer only with the bilingual context and nothing else."""

    try:
        start_time = time.time()
        response = chat_with_llm(system_prompt, user_prompt)
        logger.debug(f"为代码片段生成上下文信息，耗时 {time.time() - start_time:.2f} 秒")

        contextual_info = response.strip()

        if len(contextual_info) > 500:
            contextual_info = contextual_info[:500] + "..."

        return contextual_info

    except Exception as e:
        logger.error(f"生成上下文信息失败: {str(e)}")
        return ""

def generate_contextual_info_for_snippets(
    snippets: List[Snippet],
    max_workers: int = 4
) -> List[Snippet]:
    """
    为多个代码片段生成上下文信息，使用简单的并行处理

    参数:
        snippets: Snippet对象列表
        max_workers: 最大并行工作线程数

    返回:
        更新了上下文信息的Snippet对象列表
    """
    if not snippets:
        return snippets

    logger.info(f"开始为 {len(snippets)} 个代码片段生成上下文信息")

    # 按文件路径分组，避免重复读取同一个文件
    file_contents_cache = {}
    
    def get_file_content(snippet: Snippet) -> str:
        """获取代码片段所在文件的完整内容"""
        # 优先使用original_path，如果没有则使用file_path
        file_path = getattr(snippet, 'original_path', None) or snippet.file_path
        
        if file_path in file_contents_cache:
            return file_contents_cache[file_path]
        
        try:
            # 尝试直接读取文件
            if os.path.isfile(file_path):
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                    logger.debug(f"成功读取文件: {file_path} ({len(content)} 字符)")
            else:
                # 如果不是绝对路径，尝试使用相对路径读取
                content = read_file_with_fallback_encodings(file_path)
                logger.debug(f"通过fallback方式读取文件: {file_path} ({len(content)} 字符)")
            
            file_contents_cache[file_path] = content
            return content
        except Exception as e:
            logger.warning(f"无法读取文件 {file_path}: {str(e)}")
            file_contents_cache[file_path] = ""
            return ""

    def process_single_snippet(snippet_with_index):
        snippet, index = snippet_with_index
        try:
            content = snippet.get_snippet(add_ellipsis=False, add_lines=False)
            
            # 获取完整文件内容作为surrounding_context
            surrounding_context = get_file_content(snippet)

            contextual_info = generate_contextual_info_for_snippet(
                snippet_content=content,
                file_path=snippet.file_path,
                surrounding_context=surrounding_context
            )

            return index, contextual_info
        except Exception as e:
            logger.error(f"处理代码片段 {index} 失败: {str(e)}")
            return index, ""

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        snippet_with_indices = [(snippet, i) for i, snippet in enumerate(snippets)]

        results = list(tqdm(
            executor.map(process_single_snippet, snippet_with_indices),
            total=len(snippets),
            desc="生成上下文信息"
        ))

    for index, contextual_info in results:
        if index < len(snippets):
            snippets[index].contextual_info = contextual_info

    success_count = sum(1 for snippet in snippets if snippet.contextual_info)
    logger.info(f"成功为 {success_count}/{len(snippets)} 个代码片段生成上下文信息")
    logger.info(f"共读取了 {len(file_contents_cache)} 个不同的文件")

    return snippets
