# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_all

# 收集所有依赖项
collected_datas = []
collected_binaries = []
collected_hiddenimports = []

for package in [
    'tiktoken',
    'tiktoken_ext',
    'numpy',
    'tqdm',
    'loguru',
    'yaml',  # 即 pyyaml
    'pydantic',
    'rapidfuzz',
    'redis',
    'dotenv',  # 即 python-dotenv
    'chardet',
    'tantivy',
    'diskcache',
    'pylint',
    'tree_sitter',
    'tree_sitter_javascript',
    'tree_sitter_languages',
    'tree_sitter_python',
    'tree_sitter_typescript',
    'tree_sitter_html',
    'tree_sitter_css',
    'tree_sitter_json',
    'backoff',
    'openai',
    'scipy',
    'voyageai',
    'anthropic',
    'networkx',
    'pymongo',
    'pytz',
    'parea_ai',
    'github',  # 即 pygithub
    'git',  # 即 gitpython
    'stringzilla',
    'cohere',
    'fastapi',
    'uvicorn',
    'multipart',  # 即 python-multipart
]:
    datas, binaries, hiddenimports = collect_all(package)
    collected_datas.extend(datas)
    collected_binaries.extend(binaries)
    collected_hiddenimports.extend(hiddenimports)

a = Analysis(
    ['codemind_api.py'],
    pathex=[],
    binaries=collected_binaries,
    datas=[
        ('api', 'api'),
        ('config', 'config'),
        ('core', 'core'),
        ('dataclass', 'dataclass'),
        ('utils', 'utils'),
        ('llm', 'llm'),
        ('logn', 'logn'),
        ('agents', 'agents')
    ] + collected_datas,
    hiddenimports=collected_hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,       # Include binaries in exe for onefile mode
    a.datas,          # Include data in exe for onefile mode
    [],
    name='codemind',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    onefile=True      # Set onefile mode to True
) 