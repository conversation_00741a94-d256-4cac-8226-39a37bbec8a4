import { CodeMindBridge } from "/Users/<USER>/ai/codemind/dist/mcp/js2python/js2py.js";

console.error('begin');

// 1. 初始化 CodeMindBridge
const bridge = new CodeMindBridge("/Users/<USER>/ai/vectorx-client/packages/entrypoints/vectorx-plugin/dist/@xhs/codemind");

// const result = await bridge.prepare_search_index("/Users/<USER>/workspace/sns-rn-monorepo/apps/venom");
const result = await bridge.codebase_search("/Users/<USER>/workspace/sns-rn-monorepo/apps/venom", "storage");

console.error('end');

console.error(result);

