import { spawn } from "child_process";
import { existsSync } from "fs";
import * as path from "path";

export class PythonAdapter {
  private rootPath: string;
  private pythonExecutable: string;

  constructor(rootPath: string) {
    this.rootPath = rootPath;
    this.pythonExecutable = path.join(this.rootPath, '.venv/bin/python');
  }

  sendDataToPython(scriptPath: string, args: Array<any>) {
    return new Promise((resolve, reject) => {
      try {
        // 检查Python脚本是否存在
        const absolutePath = path.resolve(scriptPath);
        if (!existsSync(absolutePath)) {
          return reject(new Error(`Python脚本不存在: ${absolutePath}`));
        }

        console.error('sendDataToPython: ', absolutePath, args);

        const pythonProcess = spawn(this.pythonExecutable, [absolutePath, ...args]);
        let stdoutData = '';
        let stderrData = '';

        pythonProcess.stdout?.on('data', (data: any) => {
          stdoutData += data;
        });

        pythonProcess.stderr?.on('data', (data: any) => {
          stderrData += data;
        });

        pythonProcess.on('close', (code: number | null) => {
          if (code !== 0) {
            console.error(`子进程退出码 ${code}: ${stderrData}`)
            reject(`子进程退出码 ${code}: ${stderrData}`);
          } else {
            resolve(stdoutData);
          }
        });

        pythonProcess.on('error', (err: Error) => {
          console.error(`启动Python进程失败: ${err.message}`)
          reject(new Error(`启动Python进程失败: ${err.message}`));
        });

        // 发送数据到 Python 的 stdin
        if (pythonProcess.stdin) {
          pythonProcess.stdin.write("input body");
          pythonProcess.stdin.end(); // 必须调用 end() 以结束输入流
        } else {
          console.error("无法向Python进程写入数据：进程stdin不可用")
          reject(new Error("无法向Python进程写入数据：进程stdin不可用"));
        }
      } catch (error) {
        console.error(`调用Python脚本时发生错误: ${error instanceof Error ? error.message : String(error)}`)
        reject(new Error(`调用Python脚本时发生错误: ${error instanceof Error ? error.message : String(error)}`));
      }
    });
  }

  callPython(scriptPath: string, args: Array<any>) {
    return this.sendDataToPython(scriptPath, args);
  }
}

// Export the class and a backward compatible function
export default PythonAdapter;
