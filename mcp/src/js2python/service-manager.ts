import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as os from 'os';
import { createHash } from 'crypto';
import * as fs from 'fs';
import * as net from 'net';

const debugEnabled = (process.env.DEBUG_CODEMIND || "false").toLowerCase() === "true";

enum PlatformType {
    MacARM64 = 'macOS ARM64',
    MacX86_64 = 'macOS x86_64',
    WindowsX64 = 'Windows x64',
    WindowsX86 = 'Windows x86',
    Unknown = 'Unknown',
  }

  function detectPlatform(): PlatformType {
    const platform = process.platform;
    const arch = process.arch;

    // macOS 判断
    if (platform === 'darwin') {
      const darwinArch = os.arch();
      if (darwinArch === 'arm64') {
        return PlatformType.MacARM64;
      } else if (darwinArch === 'x64') {
        return PlatformType.MacX86_64;
      } else {
        return PlatformType.Unknown;
      }
    }

    // Windows 判断
    if (platform === 'win32') {
      if (arch === 'x64' || arch === 'ia32') {
        return PlatformType.WindowsX64;
      } else {
        return PlatformType.WindowsX86;
      }
    }

    return PlatformType.Unknown;
  }

interface DaemonConfig {
    repo_path: string;
    daemon_id: string;
    pid_file: string;
    socket_file: string;
    log_file: string;
    cache_dir: string;
}


/**
 * 单例服务管理器，负责管理 CodeMind 守护进程和所有 API 调用
 */
export class CodeMindServiceManager {
    private static instance: CodeMindServiceManager | null = null;
    private apiPath: string;
    private pythonExecutable: string;
    private daemonProcess: ChildProcess | null = null;
    private isInitialized: boolean = false;
    private initializationPromise: Promise<void> | null = null;

    private constructor(rootPath: string) {
        console.log('CodeMindServiceManager : 初始化 CodeMind 管理器');

        if (debugEnabled) {
            console.log('CodeMindServiceManager : debugEnabled');
            this.apiPath = path.join(rootPath, 'codemind_api.py');
            this.pythonExecutable = path.join(rootPath, '.venv/bin/python');
        } else {
            const  platform = detectPlatform();
            if (platform === PlatformType.MacARM64) {
                console.log('CodeMindServiceManager : arm64');
                this.pythonExecutable = path.join(rootPath, 'python_dist/macos/arm64/codemind');
            } else if (platform === PlatformType.MacX86_64) {
                console.log('CodeMindServiceManager : x86');
                this.pythonExecutable = path.join(rootPath, 'python_dist/macos/x86/codemind');
            } else if (platform === PlatformType.WindowsX64) {
                console.log('CodeMindServiceManager : x64');
                this.pythonExecutable = path.join(rootPath, 'python_dist/windows/x64/codemind');
            } else if (platform === PlatformType.WindowsX86) {
                console.log('CodeMindServiceManager : win x86');
                this.pythonExecutable = path.join(rootPath, 'python_dist/windows/x86/codemind');
            } else {
                throw new Error(`Unsupported platform: ${platform}`);
            }
            this.apiPath = "";
        }
    }

    private getDaemonConfig(repoPath: string): DaemonConfig {
        const HOME_DIR = os.homedir();
        const daemon_id = this.generateDaemonId(repoPath);
        const daemon_dir = path.join(HOME_DIR, ".codemind_caches/daemons", daemon_id);

        // 确保目录存在
        if (!fs.existsSync(daemon_dir)) {
            fs.mkdirSync(daemon_dir, { recursive: true });
        }

        return {
            repo_path: repoPath,
            daemon_id: daemon_id,
            pid_file: path.join(daemon_dir, "daemon.pid"),
            socket_file: path.join(daemon_dir, "daemon.sock"),
            log_file: path.join(daemon_dir, "daemon.log"),
            cache_dir: daemon_dir
        };
    }

    private generateDaemonId(repoPath: string): string {
        const absPath = path.resolve(repoPath);
        return createHash('md5').update(absPath).digest('hex').substring(0, 16);
    }

    private isDaemonRunning(repoPath: string): boolean {
        const daemonConfig = this.getDaemonConfig(repoPath);

        // 首先检查PID文件是否存在
        if (!fs.existsSync(daemonConfig.pid_file)) {
            // PID文件不存在，清理可能的残留socket文件
            this.cleanupFiles(daemonConfig);
            return false;
        }

        try {
            const pidContent = fs.readFileSync(daemonConfig.pid_file, 'utf8');
            const pid = parseInt(pidContent.trim());

            // 检查进程是否存在
            process.kill(pid, 0);

            // 进程存在，但还需要检查socket是否可用
            if (!this.testSocketConnection(daemonConfig)) {
                console.warn(`守护进程PID ${pid} 存在但socket连接失败，清理残留文件`);
                this.cleanupFiles(daemonConfig);
                return false;
            }

            return true;
        } catch (error) {
            // PID文件损坏或进程不存在，清理文件
            console.info('PID文件损坏或进程不存在，清理残留文件');
            this.cleanupFiles(daemonConfig);
            return false;
        }
    }

    private testSocketConnection(daemonConfig: DaemonConfig): boolean {
        if (!fs.existsSync(daemonConfig.socket_file)) {
            return false;
        }

        try {
            // 使用同步方式检查socket文件状态
            const stats = fs.statSync(daemonConfig.socket_file);
            if (!stats.isSocket()) {
                return false;
            }

            // 简单的连接测试 - 如果socket文件存在且是socket类型，认为可用
            // 更严格的测试会在实际发送请求时进行
            return true;
        } catch (error) {
            return false;
        }
    }

    private cleanupFiles(daemonConfig: DaemonConfig): void {
        const filesToClean = [daemonConfig.pid_file, daemonConfig.socket_file];

        for (const filePath of filesToClean) {
            if (fs.existsSync(filePath)) {
                try {
                    fs.unlinkSync(filePath);
                } catch (error) {
                    // 忽略清理错误
                }
            }
        }
    }

    private checkAndStartService(projectRoot: string): void {
        if (this.initializationPromise) {
            return;
        }

        this.initializationPromise = this.performInitialCheck(projectRoot);
    }

    private async performInitialCheck(projectRoot: string): Promise<void> {
        try {
            const isRunning = this.isDaemonRunning(projectRoot);
            if (isRunning) {
                console.log(`检测到现有 CodeMind 服务，先关闭以确保干净启动 (PID文件: ${this.getDaemonConfig(projectRoot).pid_file})`);
                await this.forceCleanupAndStart(projectRoot);
            } else {
                console.log('CodeMind 未启动，正在启动新服务...');
                await this.initializeService(projectRoot);
            }
        } catch (error) {
            console.log('检查 CodeMind 状态失败，强制清理并启动新服务...', error);
            await this.forceCleanupAndStart(projectRoot);
        }
    }

    private async forceCleanupAndStart(projectRoot: string): Promise<void> {
        console.log('开始强制清理现有服务...');

        // 1. 尝试优雅停止现有守护进程
        try {
            await this.stopDaemon();
            console.log('现有守护进程已优雅停止');
        } catch (error) {
            console.log('优雅停止失败，将强制清理:', error);
        }

        // 2. 等待一段时间确保进程完全退出
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 3. 强制清理所有残留文件
        const daemonConfig = this.getDaemonConfig(projectRoot);
        this.cleanupFiles(daemonConfig);
        console.log('残留文件已清理');

        // 4. 启动新的服务
        console.log('启动全新的 CodeMind 服务...');
        await this.initializeService(projectRoot);
    }

    public static getInstance(rootPath: string): CodeMindServiceManager {
        if (!CodeMindServiceManager.instance) {
            CodeMindServiceManager.instance = new CodeMindServiceManager(rootPath);
        }
        
        return CodeMindServiceManager.instance;
    }



    private async initializeService(projectRoot: string): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        console.log('正在启动 CodeMind...');
        await this.startDaemon(projectRoot);
        this.isInitialized = true;
        console.log('CodeMind 启动完成');
    }

    private async startDaemon(projectRoot: string): Promise<void> {
        if (this.daemonProcess) {
            console.log('CodeMind 已经在运行');
            return;
        }

        return new Promise((resolve, reject) => {
            try {
                console.log('启动 CodeMind...');

                const env = {
                    ...process.env
                };

                const args = ['--repo-path', projectRoot, 'start'];

                if (debugEnabled) {
                    this.daemonProcess = spawn(this.pythonExecutable, [
                        this.apiPath,
                        ...args
                    ], {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        cwd: projectRoot,
                        env: env
                    });
                } else {
                    this.daemonProcess = spawn(this.pythonExecutable, args, {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        cwd: projectRoot,
                        env: env
                    });
                }

                let daemonStarted = false;

                this.daemonProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    console.log('CodeMind:', output);

                    if (output.includes('守护进程启动成功') && !daemonStarted) {
                        daemonStarted = true;
                        setTimeout(() => {
                            this.waitForDaemonReady(projectRoot).then(resolve).catch(reject);
                        }, 1000);
                    }
                });

                this.daemonProcess.stderr?.on('data', (data) => {
                    const output = data.toString();
                    console.log('CodeMind:', output);
                    
                    if (output.includes('守护进程启动成功') && !daemonStarted) {
                        daemonStarted = true;
                        setTimeout(() => {
                            this.waitForDaemonReady(projectRoot).then(resolve).catch(reject);
                        }, 1000);
                    }
                });

                this.daemonProcess.on('error', (error) => {
                    console.error('CodeMind Error:', error);
                    reject(error);
                });

                this.daemonProcess.on('exit', (code) => {
                    console.log(`CodeMind Exit，退出码: ${code}`);
                    this.daemonProcess = null;
                    this.isInitialized = false;
                    this.initializationPromise = null;
                    if (!daemonStarted) {
                        reject(new Error(`CodeMind 启动失败，退出码: ${code}`));
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    private async waitForDaemonReady(projectRoot: string, maxRetries: number = 10): Promise<void> {
        for (let i = 0; i < maxRetries; i++) {
            try {
                if (this.isDaemonRunning(projectRoot)) {
                    console.log('CodeMind 已就绪');
                    return;
                }
            } catch (error) {
                // 忽略检查错误，继续重试
            }
            console.log(`等待 CodeMind 就绪... (${i + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        throw new Error('CodeMind 健康检查失败');
    }

    public async stopDaemon(): Promise<void> {
        if (this.daemonProcess) {
            console.log('停止 CodeMind...');
            this.daemonProcess.kill('SIGTERM');

            await new Promise<void>((resolve) => {
                if (this.daemonProcess) {
                    this.daemonProcess.on('exit', () => {
                        this.daemonProcess = null;
                        this.isInitialized = false;
                        this.initializationPromise = null;
                        resolve();
                    });
                } else {
                    resolve();
                }
            });

            console.log('CodeMind 已停止');
        } else {
            // 即使没有进程在运行，也要重置状态
            this.daemonProcess = null;
            this.isInitialized = false;
            this.initializationPromise = null;
            console.log('CodeMind 状态已重置');
        }
    }

    private async restartService(projectRoot: string): Promise<void> {
        console.log('检测到 CodeMind 异常，正在重启...');

        // 尝试优雅停止守护进程
        await this.stopDaemon();

        // 等待一段时间确保资源释放
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 强制清理残留文件（无论进程是否存在）
        console.log('清理守护进程残留文件...');
        const daemonConfig = this.getDaemonConfig(projectRoot);
        this.cleanupFiles(daemonConfig);

        this.isInitialized = false;
        this.initializationPromise = null;

        // 重启时不复用现有服务，直接启动新服务
        console.log('启动全新的 CodeMind 服务...');
        await this.initializeService(projectRoot);

        console.log('CodeMind 重启完成');
    }

    /**
     * 记录socket错误的详细信息
     */
    private logSocketError(error: any, context: string): void {
        const errorCode = error.code || 'UNKNOWN';
        const errorMessage = error.message || 'Unknown error';
        console.error(`${context} - Socket错误 [${errorCode}]: ${errorMessage}`);

        // 如果是Broken pipe错误，提供更多信息
        if (errorCode === 'EPIPE') {
            console.error('Broken pipe错误通常表示客户端在服务端完成响应之前断开了连接');
        }
    }

    /**
     * 检查错误是否需要重启服务
     */
    private shouldRestartService(error: any): boolean {
        // 检查是否是 Unix socket 连接错误
        if (error.code === 'ECONNREFUSED' ||  // 连接被拒绝
            error.code === 'ENOTFOUND' ||     // 找不到地址
            error.code === 'ETIMEDOUT' ||     // 连接超时
            error.code === 'ECONNRESET' ||    // 连接重置
            error.code === 'ENOENT' ||        // Unix socket 文件不存在
            error.code === 'EPIPE' ||         // 管道破裂 (Broken pipe)
            error.code === 'ECONNABORTED' ||  // 连接中止
            error.code === 'ENETDOWN' ||      // 网络不可用
            error.code === 'ENETUNREACH' ||   // 网络不可达
            error.code === 'EHOSTDOWN' ||     // 主机不可用
            error.code === 'EHOSTUNREACH') {  // 主机不可达
            return true;
        }

        // 检查错误消息
        const errorMessage = error.message?.toLowerCase() || '';
        if (errorMessage.includes('connection refused') ||
            errorMessage.includes('service unavailable') ||
            errorMessage.includes('no such file') ||
            errorMessage.includes('socket') ||
            errorMessage.includes('broken pipe') ||
            errorMessage.includes('connection reset') ||
            errorMessage.includes('connection aborted') ||
            errorMessage.includes('守护进程不可用') ||
            errorMessage.includes('连接守护进程超时') ||
            errorMessage.includes('客户端连接中断')) {
            return true;
        }

        // 特别处理：连接超时需要重启，但响应超时不需要重启
        if (errorMessage.includes('timeout')) {
            // 如果是连接超时（连接阶段的超时），需要重启
            if (errorMessage.includes('connect') ||
                errorMessage.includes('connection') ||
                errorMessage.includes('连接')) {
                return true;
            }
            // 如果是响应超时（已连接但等待响应超时），不需要重启
            if (errorMessage.includes('response') ||
                errorMessage.includes('等待响应') ||
                errorMessage.includes('响应')) {
                return false;
            }
            // 其他timeout情况，保守处理，需要重启
            return true;
        }

        return false;
    }

    /**
     * 带重试机制的 API 调用包装器
     */
    private async executeWithRetry<T>(
        apiCall: () => Promise<T>,
        methodName: string,
        projectRoot: string,
        maxRetries: number = 1
    ): Promise<T> {
        let lastError: any;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                await this.waitForServiceReady(projectRoot);
                return await apiCall();
            } catch (error) {
                lastError = error;

                // 记录详细的socket错误信息
                this.logSocketError(error, `${methodName} 调用失败 (尝试 ${attempt + 1}/${maxRetries + 1})`);

                // 如果不是最后一次尝试且错误需要重启服务
                if (attempt < maxRetries && this.shouldRestartService(error)) {
                    try {
                        await this.restartService(projectRoot);
                        console.log(`${methodName} 服务重启后准备重试...`);
                    } catch (restartError) {
                        console.error(`${methodName} 服务重启失败:`, restartError);
                        // 如果重启失败，直接抛出原始错误
                        throw lastError;
                    }
                } else {
                    // 最后一次尝试失败或不需要重启，直接抛出错误
                    throw lastError;
                }
            }
        }

        throw lastError;
    }

    public isServiceReady(): boolean {
        return this.isInitialized;
    }


    private async sendRequest(action: string, params: any = {}, response_timeout = 120000): Promise<any> {
        return new Promise((resolve, reject) => {
            const client = net.createConnection(this.getDaemonConfig(params.repo_directory).socket_file);
            let isResolved = false;

            // 设置连接超时
            const connectTimeout = setTimeout(() => {
                if (!isResolved) {
                    isResolved = true;
                    client.destroy();
                    reject(new Error('连接 CodeMind 超时'));
                }
            }, 10000); // 10秒连接超时

            client.on('connect', () => {
                clearTimeout(connectTimeout);

                const requestData = {
                    action: action,
                    params: params
                };

                const requestStr = JSON.stringify(requestData) + '\n\n';
                client.write(requestStr);

                // 设置响应超时
                const responseTimeout = setTimeout(() => {
                    if (!isResolved) {
                        isResolved = true;
                        client.destroy();
                        reject(new Error('等待响应超时'));
                    }
                }, response_timeout); // 30秒响应超时

                let responseData = '';
                client.on('data', (data: Buffer) => {
                    responseData += data.toString();
                });

                client.on('end', () => {
                    clearTimeout(responseTimeout);
                    if (isResolved) return;

                    try {
                        const response = JSON.parse(responseData);
                        isResolved = true;
                        if (response.success) {
                            resolve(response.message);
                        } else {
                            reject(new Error(response.message));
                        }
                    } catch (error) {
                        isResolved = true;
                        reject(new Error(`解析响应失败: ${error}`));
                    }
                });
            });

            client.on('error', (error: Error) => {
                clearTimeout(connectTimeout);
                if (!isResolved) {
                    isResolved = true;
                    // 为错误添加更多上下文信息
                    const enhancedError = new Error(`Socket连接错误: ${error.message}`);
                    (enhancedError as any).code = (error as any).code;
                    (enhancedError as any).originalError = error;
                    reject(enhancedError);
                }
            });
        });
    }

    /**
     * 等待服务完全准备好
     * 这个方法确保服务已经启动并可以接受请求
     * 这是调用任何 API 方法之前的最佳实践
     */
    public async waitForServiceReady(projectRoot: string): Promise<void> {
        // 如果服务已经初始化，直接返回
        if (this.isInitialized) {
            return;
        }

        // 如果有初始化 Promise，等待它完成
        if (this.initializationPromise) {
            await this.initializationPromise;
            return;
        }

        // 如果没有初始化 Promise 且服务未就绪，启动初始化
        if (!this.isInitialized && !this.initializationPromise) {
            console.log('CodeMind 未就绪，开始初始化...');
            this.checkAndStartService(projectRoot);

            // 等待初始化完成
            if (this.initializationPromise) {
                await this.initializationPromise;
            }
        }

        // 最终检查：如果服务仍未准备好，抛出错误
        if (!this.isInitialized) {
            throw new Error('服务初始化失败，无法准备就绪');
        }
    }

    public async codebase_search(
        repoPath: string,
        query: string,
        limit: number = 10
    ) {
        return this.executeWithRetry(async () => {
            return await this.sendRequest('codebase_search', {
                repo_directory: repoPath,
                query: query,
                limit: limit,
                min_score: 0.0
            }, 5 * 60 * 1000);
        }, 'codebase_search', repoPath);
    }

    public async grep_search(
        repoPath: string,
        query: string
    ) {
        return this.executeWithRetry(async () => {
            return await this.sendRequest('grep_search', {
                repo_directory: repoPath,
                query: query
            });
        }, 'grep_search', repoPath);
    }

    public async knowledge_search(
        repoPath: string,
        query: string,
        limit: number = 10,
        timeout: number = 30
    ) {
        return this.executeWithRetry(async () => {
            return await this.sendRequest('knowledge_search', {
                repo_directory: repoPath,
                query: query,
                limit: limit,
                timeout: timeout
            });
        }, 'knowledge_search', repoPath);
    }

    public async extension_search(
        repoPath: string,
        query: string
    ) {
        return this.executeWithRetry(async () => {
            return await this.sendRequest('extension_search', {
                repo_directory: repoPath,
                query: query
            });
        }, 'extension_search', repoPath);
    }

    public async prepare_search_index(
        repoPath: string,
        extensions?: string[],
        llmSummary?: boolean,
        contextualRetrieval?: boolean
    ) {
        return this.executeWithRetry(async () => {
            let extensionSources: string | undefined;

            if (extensions) {
                try {
                    if (Array.isArray(extensions) && extensions.length > 0) {
                        extensionSources = JSON.stringify(extensions);
                    }
                } catch (error) {
                    console.error(`Error reading or parsing extensions: ${extensions}`, error);
                }
            }

            return await this.sendRequest('index_codebase', {
                repo_directory: repoPath,
                extension_sources: extensionSources,
                llm_summary: llmSummary || false,
                contextual_retrieval: contextualRetrieval || false
            }, 5 * 60 * 1000);
        }, 'prepare_search_index', repoPath);
    }
}