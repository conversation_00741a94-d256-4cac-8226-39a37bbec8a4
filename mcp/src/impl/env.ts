import path from "path";

// 处理命令行参数，获取projectDir
// 命令格式：node index.js --projectDir=/path/to/directory
function getProjectDir(): string {
  const args = process.argv.slice(2);
  for (const arg of args) {
    if (arg.startsWith('--projectDir=')) {
      const projectDir = arg.substring('--projectDir='.length);
      if (projectDir) {
        // 确保路径是绝对路径
        if (path.isAbsolute(projectDir)) {
          return projectDir;
        } else {
          return path.resolve(process.cwd(), projectDir);
        }
      }
    }
  }
  // 如果没有传入projectDir或为空，则使用当前工作目录
  return process.cwd();
}

// 获取扩展源配置
// 命令格式：node index.js --extensionSources="path1,path2,path3"
// 如果没有传入extensionSources参数，则返回空数组
function getExtensionSourcesConfig(): string[] {
  const args = process.argv.slice(2);
  for (const arg of args) {
    if (arg.startsWith('--extensionSources=')) {
      const extensionSourcesStr = arg.substring('--extensionSources='.length);
      if (extensionSourcesStr) {
        // 直接按逗号分割字符串
        return extensionSourcesStr.split(',').map(s => s.trim()).filter(s => s.length > 0);
      }
    }
  }
  // 如果没有传入extensionSources参数，则返回空数组
  return [];
}

// 导出当前工作目录
export const currentWorkingDirectory = getProjectDir();
// 导出扩展源配置
export const extensionSourcesConfig = getExtensionSourcesConfig();