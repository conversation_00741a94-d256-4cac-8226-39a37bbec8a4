import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";
import { getTools as getKnowledgeTools, getToolHandlers as getKnowledgeToolHandlers } from "./impl/index.js";
import { currentWorkingDirectory, extensionSourcesConfig } from "./impl/env.js";
import path from "path";
import { fileURLToPath } from "url";

// Import the CodeMindService class
import { CodeMindService } from "./service/service.js";

// TODO: MOCK 逻辑
import { InProcessMessenger } from "./protocol/messenger/index.js";
import { FromCoreProtocol, ToCoreProtocol } from "./protocol/index.js";
import { IDE } from  "./protocol/index.js";

const inProcessMessenger = new InProcessMessenger<
      ToCoreProtocol,
      FromCoreProtocol
    >();

// 创建一个模拟的 IDE 实现
const mockIde: IDE = {
  getWorkspaceDirs: async () => {
    console.error("Mock: Getting workspace directories");
    return [currentWorkingDirectory];
  },
  getBranch: async (dir: string) => {
    console.error(`Mock: Getting branch for directory: ${dir}`);
    return "main";
  },
  getRepoName: async (dir: string) => {
    console.error(`Mock: Getting repo name for directory: ${dir}`);
    return "mock-repo";
  },
  getCodebaseExtensions: async () => {
    console.error("Mock: Getting codebase extensions:", extensionSourcesConfig);
    // 直接返回扩展目录配置数组
    return extensionSourcesConfig;
  }
};
// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '../../');

// 初始化 Service 实例
const service = new CodeMindService(inProcessMessenger, mockIde, projectRoot);

// 创建 Server 实例
const server = new Server(
  {
    name: "codemind-search-mcp",
    version: "1.0.0",
    description: "codemind-search-mcp",
    allowedDirectories: [currentWorkingDirectory],  // 允许访问当前工程目录
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 工具列表
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      ...getKnowledgeTools()
    ]
  };
});

// 处理工具执行
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  const toolHanders: {
    [tool: string]: any
  } = {
    ...getKnowledgeToolHandlers()
  }
  try {
    if (toolHanders[name]) {
      const result = await toolHanders[name](args)
      return result
    } else {
      throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(
        `Invalid arguments for ${name} tool: ${error.errors
          .map((e) => `${e.path.join(".")}: ${e.message}`)
          .join(", ")}`
      );
    }
    
    throw new Error(`Failed to perform ${name} tool, ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
});

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("CodeMind Search MCP Server running on stdio");
  console.error("当前工程目录: ", currentWorkingDirectory);
  console.error("项目根目录: ", projectRoot);
  console.error("扩展目录: ", extensionSourcesConfig);

  // TODO: 创建并发送一个 mock 事件. 后续在vertorx侧, 通过messenger 发送消息到 codemind service
  setTimeout(() => {
    console.error("发送 mock files/created 事件...");
    // 使用service.invoke触发files/created事件
    service.invoke("files/created", {
      uris: ["file1.txt", "file2.js", "file3.css"]
    });
  }, 5000); // 每隔3秒发送
}

main().catch((error) => {
  console.error("Fatal error:", error);
  process.exit(1);
});