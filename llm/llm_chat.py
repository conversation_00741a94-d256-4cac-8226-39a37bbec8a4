from openai import OpenAI
from config.server import LLM_API_KEY, LLM_API_BASE, LLM_MODEL

client = OpenAI(api_key=LLM_API_KEY, base_url=LLM_API_BASE)

def chat_with_llm(system_prompt, user_prompt):
    response = client.chat.completions.create(
        model=LLM_MODEL,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        stream=False
    )
    return response.choices[0].message.content
