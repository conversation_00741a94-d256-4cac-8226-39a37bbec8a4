"""
缓存相关常量定义

统一管理所有缓存相关的常量，避免硬编码字符串散布在各个文件中。
"""

from enum import Enum
from typing import Union


class CacheType(Enum):
    """缓存类型枚举"""
    MAIN = "main"
    EXTENSION = "extension"


class CacheNames(Enum):
    """缓存名称枚举"""
    VECTOR = "vector"
    SNIPPETS = "snippets"
    TOKEN = "token"
    CHUNK = "chunk"
    FILE_NAME = "file_name"
    LEXICAL_INDEX = "lexical_index"


# 扩展源相关常量
EXTENSION_SEED = "extension"
EXTENSION_CACHE_KEY = "extension_sources_cache"

# 缓存目录名称
CACHE_DIR_NAMES = {
    CacheNames.VECTOR: "vector_cache",
    CacheNames.SNIPPETS: "snippets_cache", 
    CacheNames.TOKEN: "token_cache",
    CacheNames.CHUNK: "chunk_cache",
    CacheNames.FILE_NAME: "file_name_cache",
    CacheNames.LEXICAL_INDEX: "lexical_index_cache"
}

# 扩展源缓存基础目录
EXTENSION_CACHE_BASE_DIR = "extension_cache"

UNIFIED_CACHE_VERSION = "v2.1.1"


def normalize_cache_type(cache_type: Union[CacheType, str]) -> CacheType:
    """
    标准化缓存类型参数
    
    参数:
        cache_type: 缓存类型，可以是 CacheType 枚举或字符串
        
    返回:
        CacheType 枚举值
        
    异常:
        ValueError: 当传入无效的缓存类型时
    """
    if isinstance(cache_type, str):
        try:
            return CacheType(cache_type)
        except ValueError:
            raise ValueError(f"无效的缓存类型: {cache_type}，支持的类型: {[t.value for t in CacheType]}")
    elif isinstance(cache_type, CacheType):
        return cache_type
    else:
        raise ValueError(f"缓存类型必须是 str 或 CacheType，得到: {type(cache_type)}")


def get_cache_dir_name(cache_name: Union[CacheNames, str]) -> str:
    """
    获取缓存目录名称
    
    参数:
        cache_name: 缓存名称
        
    返回:
        缓存目录名称
    """
    if isinstance(cache_name, str):
        # 尝试从字符串转换为枚举
        for name in CacheNames:
            if name.value == cache_name:
                cache_name = name
                break
        else:
            # 如果没找到对应的枚举，直接使用字符串
            return f"{cache_name}_cache"
    
    return CACHE_DIR_NAMES.get(cache_name, f"{cache_name.value}_cache")
